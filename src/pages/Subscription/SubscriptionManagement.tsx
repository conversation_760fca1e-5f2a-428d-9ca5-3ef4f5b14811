import { useUser } from '@clerk/clerk-react';
import { useAction, useMutation, useQuery } from 'convex/react';
import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { api } from '../../../convex/_generated/api';
import { PlanChangeConfirmationModal, PlanComparison, PortalReturnHandler, SubscriptionCancellationModal, SubscriptionReactivationModal, usePortalReturn } from '../../components/subscription';
import { TrialPlanSelectionModal } from '../../components/subscription/TrialPlanSelectionModal';
import { BodyText, Heading2, PageLayout, PrimaryButton, SecondaryButton, TextMuted } from '../../components/ui';
import { CheckoutButton, CheckoutError, CheckoutLoading } from '../../components/ui/CheckoutButton';
import { createPlanCheckoutParams, useCheckout } from '../../hooks/useCheckout';
import { useSubscriptionAccess } from '../../hooks/useSubscriptionAccess';
import { useSubscriptionReactivation } from '../../hooks/useSubscriptionReactivation';
import { useUserRole } from '../../hooks/useUserRole';
import { getSubscriptionStatusInfo } from '../../utils/subscriptionStatusFormatting';
import { formatTimeRemaining, getDaysLeft } from '../../utils/timeFormatting';

const SubscriptionManagementFixed: React.FC = () => {
  console.log('🔥 Frontend: SubscriptionManagement component loaded');
  const { user } = useUser();
  const navigate = useNavigate();
  const { isAdministrator, user: userWithRole, isLoading: roleLoading } = useUserRole();
  const { subscription, isInTrial, isLoading: subLoading } = useSubscriptionAccess();

  // Check if user returned from Stripe portal and force refresh
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible') {
        const portalOpenTime = sessionStorage.getItem('stripe-portal-opened');
        if (portalOpenTime) {
          const timeSincePortal = Date.now() - parseInt(portalOpenTime);
          // If less than 5 minutes since portal was opened, force refresh
          if (timeSincePortal < 5 * 60 * 1000) {
            console.log('🔄 User returned from Stripe portal, forcing subscription refresh');
            // Trigger a subscription update event
            window.dispatchEvent(new CustomEvent('subscription-updated', {
              detail: { source: 'stripe-portal-return' }
            }));
            // Clear the portal timestamp
            sessionStorage.removeItem('stripe-portal-opened');
            // Force page reload to ensure fresh data
            window.location.reload();
          }
        }
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, []);


  const createPortalSession = useAction(api.subscriptions.createPortalSession);
  const createTrialSubscription = useMutation(api.subscriptions.createTrialSubscription);
  const [isUpgrading, setIsUpgrading] = useState(false);
  const [showTrialPlanModal, setShowTrialPlanModal] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Handle proceeding to checkout from trial plan selection modal
  const handleProceedToCheckout = async (planLevel: string, billingInterval: 'month' | 'year') => {
    if (!user) {
      console.error('No user found');
      return;
    }

    console.log('🔥 Frontend: Proceeding to checkout with plan:', planLevel, billingInterval);
    setShowTrialPlanModal(false);
    setIsUpgrading(true);

    try {
      // Create checkout parameters for selected plan
      const checkoutParams = createPlanCheckoutParams(planLevel as 'basic' | 'professional' | 'enterprise', billingInterval, {
        trialDays: 0, // No trial for upgrade
      });

      if (checkoutParams.priceId) {
        await checkout.initiateCheckout({
          priceId: checkoutParams.priceId,
          planLevel: checkoutParams.planLevel!,
          billingInterval: checkoutParams.billingInterval!,
          quantity: checkoutParams.quantity,
          successUrl: checkoutParams.successUrl,
          cancelUrl: checkoutParams.cancelUrl,
          allowPromotionCodes: checkoutParams.allowPromotionCodes,
          automaticTax: checkoutParams.automaticTax,
        });
      }
    } catch (error) {
      console.error('Checkout failed:', error);
    } finally {
      setIsUpgrading(false);
    }
  };

  // Plan change functionality
  const [showPlanComparison, setShowPlanComparison] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);
  const [planChangeDetails, setPlanChangeDetails] = useState<any>(null);
  const [isProcessingPlanChange, setIsProcessingPlanChange] = useState(false);

  const calculateProration = useQuery(api.planChanges?.calculatePlanChangeProration, "skip");
  const initiatePlanChange = useMutation(api.planChanges?.initiatePlanChange);
  const canChangeToPlan = useQuery(api.planChanges?.canChangeToPlan, "skip");

  // Cancellation functionality
  const [showCancellationModal, setShowCancellationModal] = useState(false);
  const [isProcessingCancellation, setIsProcessingCancellation] = useState(false);

  const canCancelSubscription = useQuery(
    api.subscriptionCancellation?.canCancelSubscription,
    user ? { userId: user.id } : "skip"
  );
  const initiateCancellation = useMutation(api.subscriptionCancellation?.initiateSubscriptionCancellation);

  // Reactivation functionality
  const reactivation = useSubscriptionReactivation();
  const canReactivate = useQuery(
    api.subscriptionReactivation?.canReactivateSubscription,
    user ? { userId: user.id } : "skip"
  );
  const availablePlans = useQuery(
    api.subscriptionReactivation?.getReactivationPlans,
    user ? { userId: user.id } : "skip"
  );

  // Portal return handling
  const portalReturn = usePortalReturn();

  // Checkout hook for direct Stripe integration
  const checkout = useCheckout();

  // Portal management function
  const openPortal = async (_flow?: string) => {
    if (!user) return;

    setIsLoading(true);
    try {
      const result = await createPortalSession({
        userId: user.id,
        returnUrl: `${window.location.origin}/subscription`
      });

      if (result.url) {
        // Store the time when we opened the portal
        sessionStorage.setItem('stripe-portal-opened', Date.now().toString());
        window.location.href = result.url;
      }
    } catch (error) {
      console.error('Failed to open billing portal:', error);
      alert('Kunne ikke åpne faktureringsportal. Prøv igjen senere.');
    } finally {
      setIsLoading(false);
    }
  };



  // Show loading state while checking permissions
  if (roleLoading || subLoading) {
    return (
      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            <div className="flex items-center justify-center py-16">
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                <TextMuted>Sjekker tilganger...</TextMuted>
              </div>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Debug information (remove in production)
  console.log('Debug - Subscription Management State:', {
    user: user?.id,
    isAdministrator,
    subscription: subscription ? 'Found' : 'Not found',
    isInTrial,
    subscriptionDetails: subscription ? {
      id: (subscription as any)?._id,
      userId: (subscription as any)?.userId,
      stripeCustomerId: (subscription as any)?.stripeCustomerId,
      status: (subscription as any)?.status,
      planLevel: (subscription as any)?.planLevel
    } : null
  });

  // Check administrator access
  if (!isAdministrator) {
    return (
      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            <div className="text-center py-16">
              <div className="w-16 h-16 bg-jobblogg-warning-soft rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-jobblogg-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <Heading2 className="mb-4">Kun for administratorer</Heading2>
              <BodyText className="mb-8 text-jobblogg-text-medium max-w-md mx-auto">
                Denne siden er kun tilgjengelig for brukere med administratorrettigheter. 
                Kontakt din administrator hvis du trenger tilgang.
              </BodyText>
              <BodyText className="text-sm text-jobblogg-text-muted">
                Du er logget inn som {(userWithRole as any)?.role === 'prosjektleder' ? 'prosjektleder' : 'utførende bruker'}
              </BodyText>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }

  // Define handleUpgrade function before it's used
  const handleUpgrade = async () => {
    if (!user) {
      console.error('No user found');
      return;
    }

    console.log('🔥 Frontend: Starting upgrade process for user:', user.id);
    setIsUpgrading(true);

    try {
      // For trial users, open plan selection modal for better UX
      if (isInTrial && subscription) {
        console.log('🔥 Frontend: Trial user - opening plan selection modal');
        setShowTrialPlanModal(true);
        setIsUpgrading(false);
        return;
      }

      // For existing subscribers, use Customer Portal
      console.log('🔥 Frontend: Existing subscriber - using Customer Portal');
      const result = await createPortalSession({
        userId: user.id,
        returnUrl: window.location.href
      });

      console.log('Portal session result:', result);

      if (result?.url) {
        console.log('Redirecting to:', result.url);
        window.location.href = result.url;
      } else {
        console.error('No URL returned from createPortalSession');
        setIsUpgrading(false);
      }
    } catch (error: unknown) {
      console.error('Failed to create upgrade session:', error);

      // If no subscription found, try to create a trial subscription first
      if ((error as any).message?.includes('No subscription found')) {
        console.log('No subscription found, creating trial subscription...');
        try {
          const trialResult = await createTrialSubscription({
            userId: user.id,
            name: user.fullName || `${user.firstName} ${user.lastName}` || 'Bruker',
            email: user.primaryEmailAddress?.emailAddress || '',
            selectedPlan: 'basic'
          });

          console.log('Trial subscription created:', trialResult);

          // Refresh the page to update subscription status
          window.location.reload();
        } catch (trialError) {
          console.error('Failed to create trial subscription:', trialError);
          setIsUpgrading(false);
          alert(`Test failed: ${(trialError as any).message}`);
        }
      } else {
        setIsUpgrading(false);
        alert('Det oppstod en feil. Prøv igjen senere.');
      }
    }
  };

  // Plan change handlers
  const handlePlanSelect = async (planId: string, billingInterval: 'month' | 'year') => {
    if (!user || !api.planChanges?.calculatePlanChangeProration) return;

    try {
      const prorationDetails = await calculateProration({
        userId: user.id,
        newPlanId: planId,
        newBillingInterval: billingInterval,
      });

      setPlanChangeDetails(prorationDetails);
      setShowConfirmationModal(true);
      setShowPlanComparison(false);
    } catch (error) {
      console.error('Failed to calculate proration:', error);
      alert('Kunne ikke beregne prisendring. Prøv igjen senere.');
    }
  };

  const handleConfirmPlanChange = async () => {
    if (!user || !planChangeDetails || !api.planChanges?.initiatePlanChange) return;

    setIsProcessingPlanChange(true);

    try {
      const result = await initiatePlanChange({
        userId: user.id,
        newPlanId: planChangeDetails.newPlan.id,
        newBillingInterval: planChangeDetails.newPlan.billingInterval,
      });

      if (result.status === 'initiated') {
        setShowConfirmationModal(false);
        setPlanChangeDetails(null);

        // Show success message and refresh after a delay
        alert('Planendring er startet! Siden vil oppdateres om et øyeblikk.');
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to initiate plan change:', error);
      alert('Planendring feilet. Prøv igjen senere.');
    } finally {
      setIsProcessingPlanChange(false);
    }
  };

  const handleQuickUpgrade = () => {
    setShowPlanComparison(true);
  };

  const handleQuickDowngrade = () => {
    setShowPlanComparison(true);
  };

  // Cancellation handlers
  const handleCancelSubscription = async (feedback: any, cancelImmediately: boolean) => {
    if (!user || !api.subscriptionCancellation?.initiateSubscriptionCancellation) return;

    setIsProcessingCancellation(true);

    try {
      const result = await initiateCancellation({
        userId: user.id,
        feedback,
        cancelImmediately,
      });

      if (result.status === 'initiated') {
        setShowCancellationModal(false);

        // Show success message and refresh after a delay
        alert(result.message + ' Siden vil oppdateres om et øyeblikk.');
        setTimeout(() => {
          window.location.reload();
        }, 2000);
      }
    } catch (error) {
      console.error('Failed to cancel subscription:', error);
      alert('Avbryting feilet. Prøv igjen senere.');
    } finally {
      setIsProcessingCancellation(false);
    }
  };

  // No subscription state
  if (!subscription) {
    // Check if user can reactivate a cancelled subscription
    if (canReactivate?.canReactivate && canReactivate.subscription) {
      const cancelledSub = canReactivate.subscription;
      const isDataExpired = cancelledSub.isDataExpired;
      const daysUntilExpiry = Math.max(0, Math.ceil(
        (new Date(cancelledSub.dataRetentionUntil).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
      ));

      return (
        <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
          <div className="bg-white rounded-xl shadow-soft p-8">
            <div className="max-w-2xl mx-auto">
              <div className="text-center py-16 space-y-6">
                <div className="w-16 h-16 bg-jobblogg-success-soft rounded-full flex items-center justify-center mx-auto mb-6">
                  <svg className="w-8 h-8 text-jobblogg-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </div>

                <div>
                  <Heading2 className="mb-4">Velkommen tilbake!</Heading2>
                  <BodyText className="mb-6 text-jobblogg-text-medium max-w-md mx-auto">
                    Vi ser at du tidligere hadde et {cancelledSub.planLevel}-abonnement.
                    Reaktiver abonnementet ditt og få 15% rabatt på første måned!
                  </BodyText>
                </div>

                {/* Data retention status */}
                {isDataExpired ? (
                  <div className="bg-jobblogg-warning-light border border-jobblogg-warning rounded-lg p-4 mb-6">
                    <p className="text-sm text-jobblogg-text">
                      ⚠️ Dataene dine er slettet, men du kan fortsatt reaktivere abonnementet og starte på nytt.
                    </p>
                  </div>
                ) : daysUntilExpiry <= 7 ? (
                  <div className="bg-jobblogg-error-light border border-jobblogg-error rounded-lg p-4 mb-6">
                    <p className="text-sm text-jobblogg-text">
                      🚨 Dataene dine slettes om {daysUntilExpiry} dager! Reaktiver nå for å beholde alle prosjektene dine.
                    </p>
                  </div>
                ) : (
                  <div className="bg-jobblogg-success-light border border-jobblogg-success rounded-lg p-4 mb-6">
                    <p className="text-sm text-jobblogg-text">
                      ✅ Alle prosjektene dine er trygge og vil være tilgjengelige umiddelbart etter reaktivering.
                    </p>
                  </div>
                )}

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <PrimaryButton
                    onClick={reactivation.actions.openReactivationFlow}
                    className="px-8"
                    disabled={reactivation.state.isProcessing}
                  >
                    {reactivation.state.isProcessing ? 'Behandler...' : 'Reaktiver abonnement'}
                  </PrimaryButton>
                  <SecondaryButton onClick={handleUpgrade} className="px-8">
                    Start nytt abonnement
                  </SecondaryButton>
                </div>

                <div className="text-xs text-jobblogg-text-muted">
                  Avbrutt: {new Date(cancelledSub.canceledAt || cancelledSub.updatedAt).toLocaleDateString('nb-NO')}
                </div>
              </div>
            </div>
          </div>

          {/* Reactivation Modal */}
          <SubscriptionReactivationModal
            isOpen={reactivation.state.showModal}
            onClose={reactivation.actions.closeReactivationFlow}
            onReactivate={reactivation.actions.submitReactivation}
            cancelledSubscription={{
              planLevel: cancelledSub.planLevel,
              cancelledAt: cancelledSub.canceledAt || cancelledSub.updatedAt,
              dataRetentionUntil: cancelledSub.dataRetentionUntil,
            }}
            availablePlans={availablePlans || []}
            isProcessing={reactivation.state.isProcessing}
            hasValidPaymentMethod={false} // Will be handled in checkout flow
          />
        </PageLayout>
      );
    }

    // No subscription and no reactivation available
    return (
      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="medium" showFooter={false} className="bg-jobblogg-surface">
        <div className="bg-white rounded-xl shadow-soft p-8">
          <div className="max-w-2xl mx-auto">
            <div className="text-center py-16">
              <div className="w-16 h-16 bg-jobblogg-primary-soft rounded-full flex items-center justify-center mx-auto mb-6">
                <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <Heading2 className="mb-4">Ingen aktiv plan</Heading2>
              <BodyText className="mb-8 text-jobblogg-text-medium max-w-md mx-auto">
                Du har ikke et aktivt abonnement. Start en gratis prøveperiode for å få tilgang til alle funksjoner.
              </BodyText>
              <PrimaryButton onClick={handleUpgrade} className="px-8">
                Start gratis prøveperiode
              </PrimaryButton>
            </div>
          </div>
        </div>
      </PageLayout>
    );
  }







  // Calculate trial progress
  const trialStart = (subscription as any)?.trialStart || 0;
  const trialEnd = (subscription as any)?.trialEnd || 0;
  const now = Date.now();
  const totalTrialDuration = trialEnd - trialStart;
  const elapsed = now - trialStart;
  const trialProgress = Math.min(100, Math.max(0, (elapsed / totalTrialDuration) * 100));
  const timeRemaining = formatTimeRemaining(trialEnd);
  const daysLeft = getDaysLeft(trialEnd); // For backward compatibility
  const isExpiringSoon = timeRemaining.isUrgent || daysLeft <= 2;

  // Get dynamic subscription status information
  const subscriptionStatusInfo = getSubscriptionStatusInfo(subscription);

  // Check if subscription is scheduled for cancellation
  const isScheduledForCancellation = subscription && (subscription as any).cancelAtPeriodEnd === true;

  // Plan information with pricing and user limits
  const planNames = {
    basic: 'Liten bedrift',
    professional: 'Mellomstor bedrift',
    enterprise: 'Stor bedrift'
  };

  const planPrices = {
    basic: { monthly: 299, yearly: 2870 },
    professional: { monthly: 999, yearly: 9590 },
    enterprise: { monthly: 2999, yearly: 28790 }
  };

  const planUserLimits = {
    basic: 9,
    professional: 49,
    enterprise: 249
  };

  const billingLabels = {
    month: 'månedlig',
    year: 'årlig'
  };

  const currentPlan = {
    name: planNames[(subscription as any)?.planLevel as keyof typeof planNames] || (subscription as any)?.planLevel,
    level: (subscription as any)?.planLevel,
    billing: (subscription as any)?.billingInterval,
    price: planPrices[(subscription as any)?.planLevel as keyof typeof planPrices]?.[(subscription as any)?.billingInterval === 'year' ? 'yearly' : 'monthly'] || 0,
    billingLabel: billingLabels[(subscription as any)?.billingInterval as keyof typeof billingLabels] || 'månedlig',
    userLimit: planUserLimits[(subscription as any)?.planLevel as keyof typeof planUserLimits] || 9
  };

  return (
    <>
      {/* Checkout Loading Overlay */}
      <CheckoutLoading
        isVisible={checkout.state.isLoading || checkout.state.isRetrying}
        message="Starter oppgradering..."
        isRetrying={checkout.state.isRetrying}
      />

      <PageLayout title="Abonnement" showBackButton backUrl="/" containerWidth="narrow" showFooter={false} className="bg-jobblogg-surface">
        <div className="max-w-lg mx-auto px-4 py-5 space-y-8">
          <h1 className="text-3xl font-bold text-jobblogg-text-strong">Abonnement</h1>

          {/* Current Plan Hero Card - Modern Mobile-First Design */}
          <div className={`relative overflow-hidden rounded-2xl p-6 text-white shadow-lg ${
            isInTrial
              ? (isExpiringSoon
                  ? 'bg-gradient-to-br from-amber-500 to-orange-600'
                  : 'bg-gradient-to-br from-jobblogg-primary to-blue-700')
              : isScheduledForCancellation
                ? 'bg-gradient-to-br from-amber-600 to-orange-700'
                : 'bg-gradient-to-br from-jobblogg-primary to-blue-700'
          }`}>
            {/* Decorative background element */}
            <div className="absolute top-0 right-0 w-24 h-24 bg-white/10 rounded-full transform translate-x-8 -translate-y-8"></div>

            <div className="relative z-10">
              <div className="flex justify-between items-start mb-4">
                <div className="flex-1">
                  <div className="text-lg font-semibold mb-1 opacity-90">
                    {isInTrial ? (
                      timeRemaining.text === 'Prøveperioden er utløpt'
                        ? 'Prøveperioden er utløpt'
                        : currentPlan.name || 'Prøveperiode'
                    ) : (
                      currentPlan.name || 'Abonnement'
                    )}
                  </div>
                  {!isInTrial && subscription && (
                    <div className="text-3xl font-bold leading-none">
                      kr {subscription.amount?.toLocaleString('nb-NO') || '999'}
                      <span className="text-base font-normal opacity-90 ml-1">
                        /{subscription.billingInterval === 'year' ? 'år' : 'måned'}
                      </span>
                    </div>
                  )}
                  {isInTrial && (
                    <div className="text-2xl font-bold leading-none">
                      {timeRemaining.text === 'Prøveperioden er utløpt'
                        ? 'Utløpt'
                        : timeRemaining.text}
                    </div>
                  )}
                </div>

                <div className="bg-white/20 backdrop-blur-sm px-3 py-1.5 rounded-full text-sm font-medium border border-white/20">
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${
                      isScheduledForCancellation
                        ? 'bg-amber-400'
                        : 'bg-green-400'
                    }`}></div>
                    <span>
                      {isInTrial
                        ? 'Prøveperiode'
                        : isScheduledForCancellation
                          ? 'Avsluttes'
                          : 'Aktiv'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Plan Details Grid - Mobile Optimized */}
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="space-y-1">
                  <div className="opacity-75 text-xs uppercase tracking-wide">
                    {isInTrial
                      ? 'Utløper'
                      : isScheduledForCancellation
                        ? 'Avsluttes'
                        : 'Neste faktura'}
                  </div>
                  <div className="font-semibold">
                    {subscriptionStatusInfo.expirationDate ||
                     (subscription?.currentPeriodEnd ? (
                       isScheduledForCancellation ? (
                         // For scheduled cancellations, show both date and time
                         <>
                           <div>{new Date(subscription.currentPeriodEnd).toLocaleDateString('nb-NO')}</div>
                           <div className="text-xs opacity-75 mt-0.5">
                             Kl. {new Date(subscription.currentPeriodEnd).toLocaleTimeString('nb-NO', {
                               hour: '2-digit',
                               minute: '2-digit'
                             })}
                           </div>
                         </>
                       ) : (
                         // For regular subscriptions, show only date
                         new Date(subscription.currentPeriodEnd).toLocaleDateString('nb-NO')
                       )
                     ) : 'Ikke tilgjengelig')}
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="opacity-75 text-xs uppercase tracking-wide">Periode</div>
                  <div className="font-semibold">
                    {isInTrial ? 'Prøveperiode' :
                     (subscription?.billingInterval === 'year' ? 'Årlig' : 'Månedlig')}
                  </div>
                </div>

                <div className="space-y-1 col-span-2">
                  <div className="opacity-75 text-xs uppercase tracking-wide">Opprettet</div>
                  <div className="font-semibold">
                    {subscription?.createdAt ?
                      new Date(subscription.createdAt).toLocaleDateString('nb-NO') :
                      'Ikke tilgjengelig'}
                  </div>
                </div>
              </div>

              {/* Cancellation Notice - Integrated Design */}
              {!isInTrial && isScheduledForCancellation && (
                <div className="mt-6 p-4 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20">
                  <div className="flex items-start gap-3">
                    <div className="w-5 h-5 bg-amber-400/20 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <svg className="w-3 h-3 text-amber-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z" />
                      </svg>
                    </div>
                    <div className="flex-1">
                      <div className="text-sm font-medium text-white/95 mb-1">
                        Abonnement planlagt for avslutning
                      </div>
                      <div className="text-xs text-white/80 leading-relaxed">
                        Abonnementet ditt vil avsluttes {subscription?.currentPeriodEnd ?
                          `${new Date(subscription.currentPeriodEnd).toLocaleDateString('nb-NO')} kl. ${new Date(subscription.currentPeriodEnd).toLocaleTimeString('nb-NO', { hour: '2-digit', minute: '2-digit' })}` :
                          'ved slutten av gjeldende periode'}.
                        Har du ombestemt deg? Du kan enkelt angre avslutningen ved å bruke faktureringsportalen.
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Trial Progress Bar */}
              {isInTrial && (
                <div className="mt-6">
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div
                      className="h-2 rounded-full bg-white transition-all duration-300"
                      style={{ width: `${trialProgress}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Billing Portal Section - Modern Card Design */}
          {!isInTrial && (
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-5">
                <div className="w-10 h-10 bg-jobblogg-primary/10 rounded-xl flex items-center justify-center mr-4">
                  <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-jobblogg-text-strong">Faktureringsportal</h3>
                </div>
              </div>

              <p className="text-jobblogg-text-medium text-sm mb-4">
                Administrer alt som har med fakturering å gjøre:
              </p>

              <ul className="space-y-2 text-sm text-jobblogg-text-medium mb-6">
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-jobblogg-primary rounded-full mr-3 flex-shrink-0"></span>
                  Se gjeldende abonnement
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-jobblogg-primary rounded-full mr-3 flex-shrink-0"></span>
                  Kansellere abonnement
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-jobblogg-primary rounded-full mr-3 flex-shrink-0"></span>
                  Se lagret betalingsmetode
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-jobblogg-primary rounded-full mr-3 flex-shrink-0"></span>
                  Legge til ny betalingsmetode
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-jobblogg-primary rounded-full mr-3 flex-shrink-0"></span>
                  Se fakturainformasjon
                </li>
                <li className="flex items-center">
                  <span className="w-1.5 h-1.5 bg-jobblogg-primary rounded-full mr-3 flex-shrink-0"></span>
                  Oppdatere fakturainformasjon
                </li>
              </ul>

              <button
                onClick={() => openPortal()}
                disabled={isLoading}
                className="w-full bg-gradient-to-r from-jobblogg-primary to-blue-600 text-white rounded-xl px-6 py-4 font-semibold hover:from-jobblogg-primary-dark hover:to-blue-700 transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg disabled:opacity-50 disabled:transform-none"
              >
                {isLoading ? 'Åpner portal...' : 'Åpne faktureringsportal'}
              </button>
            </div>
          )}

          {/* Trial Plan Selection - Modern Card Design */}
          {isInTrial && (
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-5">
                <div className="w-10 h-10 bg-jobblogg-primary/10 rounded-xl flex items-center justify-center mr-4">
                  <svg className="w-5 h-5 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-jobblogg-text-strong">Velg abonnement</h3>
                </div>
              </div>

              <p className="text-jobblogg-text-medium text-sm mb-6">
                Velg månedlig eller årlig betaling og få full tilgang til alle funksjoner.
              </p>

              <CheckoutButton
                onClick={handleUpgrade}
                isLoading={checkout.state.isLoading}
                isRetrying={checkout.state.isRetrying}
                disabled={isUpgrading}
                loadingText="Starter..."
                className="w-full bg-gradient-to-r from-jobblogg-primary to-blue-600 text-white rounded-xl px-6 py-4 font-semibold hover:from-jobblogg-primary-dark hover:to-blue-700 transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg"
              >
                Velg abonnement
              </CheckoutButton>
            </div>
          )}




          {/* Trial Benefits Section - Modern Card Design */}
          {isInTrial && (
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-5 text-center">
                Hva får du tilgang til?
              </h3>

              <div className="space-y-4 mb-6">
                <div className="flex items-center">
                  <span className="text-green-600 font-bold mr-3">✓</span>
                  <span className="text-jobblogg-text-medium">Ubegrenset prosjektdokumentasjon</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-600 font-bold mr-3">✓</span>
                  <span className="text-jobblogg-text-medium">Teamsamarbeid og invitasjoner</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-600 font-bold mr-3">✓</span>
                  <span className="text-jobblogg-text-medium">Kundesamtaler og deling</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-600 font-bold mr-3">✓</span>
                  <span className="text-jobblogg-text-medium">Velg månedlig eller årlig betaling</span>
                </div>
                <div className="flex items-center">
                  <span className="text-green-600 font-bold mr-3">✓</span>
                  <span className="text-jobblogg-text-medium">Endre eller avslutt ved slutten av perioden</span>
                </div>
              </div>
            </div>
          )}

          {/* Checkout Error Display */}
          {checkout.state.error && (
            <CheckoutError
              error={checkout.state.error}
              norwegianError={checkout.state.norwegianError}
              canRetry={checkout.state.canRetry}
              onRetry={checkout.retryCheckout}
              onClear={checkout.clearError}
              retryCount={checkout.state.retryCount}
              maxRetries={3}
            />
          )}



          {/* Advanced Plan Management - Modern Card Design */}
          {!isInTrial && (
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <div className="flex items-center mb-5">
                <div className="w-10 h-10 bg-green-100 rounded-xl flex items-center justify-center mr-4">
                  <svg className="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"/>
                  </svg>
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-jobblogg-text-strong">Avanserte planendringer</h3>
                </div>
              </div>

              {/* Plan Options */}
              <div className="space-y-4 mb-6">
                {/* Upgrade Options */}
                {subscription?.planLevel !== 'enterprise' && (
                  <div className="border-2 border-gray-200 rounded-2xl p-5 hover:border-jobblogg-primary hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <h4 className="font-semibold text-jobblogg-text-strong mb-1">
                          {subscription?.planLevel === 'basic' ? 'Oppgrader til Professional' : 'Oppgrader til Enterprise'}
                        </h4>
                        <p className="text-sm text-jobblogg-text-medium">
                          {subscription?.planLevel === 'basic' ? 'Fra 999 NOK/måned' : 'Fra 2999 NOK/måned'}
                        </p>
                      </div>
                      <button
                        onClick={handleQuickUpgrade}
                        disabled={isProcessingPlanChange}
                        className="bg-jobblogg-primary text-white px-4 py-2 rounded-lg font-medium hover:bg-jobblogg-primary-dark transition-colors disabled:opacity-50"
                      >
                        Oppgrader
                      </button>
                    </div>
                  </div>
                )}

                {/* Downgrade Options */}
                {subscription?.planLevel !== 'basic' && (
                  <div className="border-2 border-gray-200 rounded-2xl p-5 hover:border-jobblogg-primary hover:shadow-md transition-all duration-200">
                    <div className="flex justify-between items-center">
                      <div className="flex-1">
                        <h4 className="font-semibold text-jobblogg-text-strong mb-1">
                          {subscription?.planLevel === 'enterprise' ? 'Nedgrader til Professional' : 'Nedgrader til Basic'}
                        </h4>
                        <p className="text-sm text-jobblogg-text-medium">
                          {subscription?.planLevel === 'enterprise' ? 'Fra 999 NOK/måned' : 'Fra 299 NOK/måned'}
                        </p>
                      </div>
                      <button
                        onClick={handleQuickDowngrade}
                        disabled={isProcessingPlanChange}
                        className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-200 transition-colors disabled:opacity-50"
                      >
                        Nedgrader
                      </button>
                    </div>
                  </div>
                )}
              </div>

              <button
                onClick={() => setShowPlanComparison(true)}
                className="w-full bg-green-600 text-white rounded-xl px-6 py-4 font-semibold hover:bg-green-700 transition-all duration-200 transform hover:-translate-y-0.5 hover:shadow-lg"
              >
                Se alle planer og funksjoner
              </button>

              {/* Benefits Section */}
              <div className="mt-6 pt-6 border-t border-gray-100">
                <h4 className="font-semibold text-jobblogg-text-strong mb-4">Fordeler med planendring</h4>
                <ul className="space-y-3 text-sm text-jobblogg-text-medium">
                  <li className="flex items-start">
                    <span className="text-green-600 font-bold mr-3 mt-0.5">✓</span>
                    <span>Endringer trer i kraft umiddelbart</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-600 font-bold mr-3 mt-0.5">✓</span>
                    <span>Forholdsmessig fakturering for oppgraderinger</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-600 font-bold mr-3 mt-0.5">✓</span>
                    <span>Ingen bindingstid - endre når som helst</span>
                  </li>
                  <li className="flex items-start">
                    <span className="text-green-600 font-bold mr-3 mt-0.5">✓</span>
                    <span>Beholder alle data ved planendringer</span>
                  </li>
                </ul>
              </div>
            </div>
          )}



          {/* Removed separate cancellation warning - now integrated in main subscription card */}

          {/* FAQ Section - Modern Card Design */}
          {isInTrial && (
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-100">
              <h3 className="text-lg font-semibold text-jobblogg-text-strong mb-6">Ofte stilte spørsmål</h3>

              <div className="space-y-6">
                <div className="border-b border-gray-100 pb-4 last:border-b-0 last:pb-0">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">
                    Hva skjer når prøveperioden utløper?
                  </h4>
                  <p className="text-sm text-jobblogg-text-medium leading-relaxed">
                    Du mister tilgang til JobbLogg til du velger en betalingsplan. Alle data lagres trygt og blir tilgjengelige igjen når du oppgraderer.
                  </p>
                </div>

                <div className="border-b border-gray-100 pb-4 last:border-b-0 last:pb-0">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">
                    Kan jeg si opp når som helst?
                  </h4>
                  <p className="text-sm text-jobblogg-text-medium leading-relaxed">
                    Ja, du kan si opp abonnementet ditt. Oppsigelsen gjelder fra slutten av inneværende betalingsperiode – enten månedlig eller årlig, avhengig av hvilken plan ({currentPlan.name}) du har valgt.
                  </p>
                </div>

                <div className="border-b border-gray-100 pb-4 last:border-b-0 last:pb-0">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">
                    Hvilken plan passer best for meg?
                  </h4>
                  <p className="text-sm text-jobblogg-text-medium leading-relaxed">
                    Du har valgt {currentPlan.name} med {currentPlan.billingLabel} fakturering. Planen inkluderer {currentPlan.userLimit} brukere. Etter prøveperioden vil prisen være {currentPlan.price.toLocaleString('nb-NO')} NOK ekskl. MVA. Du kan oppgradere eller nedgradere til en annen plan når som helst dersom du trenger flere eller færre brukere.
                  </p>
                </div>

                <div className="border-b border-gray-100 pb-4 last:border-b-0 last:pb-0">
                  <h4 className="font-semibold text-jobblogg-text-strong mb-2">
                    Er mine data trygge?
                  </h4>
                  <p className="text-sm text-jobblogg-text-medium leading-relaxed">
                    Ja. Alle data lagres sikkert og er kryptert. Vi følger GDPR og norske personvernregler.
                  </p>
                </div>
              </div>
            </div>
          )}


        </div>

      {/* Plan Comparison Modal */}
      {showPlanComparison && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <Heading2>Velg ny plan</Heading2>
                <button
                  onClick={() => setShowPlanComparison(false)}
                  className="text-jobblogg-text-muted hover:text-jobblogg-text"
                >
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <PlanComparison
                currentPlan={subscription.planLevel || 'basic'}
                currentBillingInterval={subscription.billingInterval || 'month'}
                onPlanSelect={handlePlanSelect}
                isLoading={isProcessingPlanChange}
              />
            </div>
          </div>
        </div>
      )}

      {/* Trial Plan Selection Modal */}
      <TrialPlanSelectionModal
        isOpen={showTrialPlanModal}
        onClose={() => setShowTrialPlanModal(false)}
        onProceedToCheckout={handleProceedToCheckout}
        currentPlan={(subscription as any)?.planLevel || 'professional'}
        currentBilling={(subscription as any)?.billingInterval || 'month'}
        isLoading={isUpgrading}
      />

      {/* Plan Change Confirmation Modal */}
      <PlanChangeConfirmationModal
        isOpen={showConfirmationModal}
        onClose={() => {
          setShowConfirmationModal(false);
          setPlanChangeDetails(null);
        }}
        onConfirm={handleConfirmPlanChange}
        planChangeDetails={planChangeDetails}
        isProcessing={isProcessingPlanChange}
      />

      {/* Subscription Cancellation Modal */}
      <SubscriptionCancellationModal
        isOpen={showCancellationModal}
        onClose={() => setShowCancellationModal(false)}
        onCancel={handleCancelSubscription}
        subscriptionDetails={subscription ? {
          planName: subscription.planLevel || 'Basic',
          nextBillingDate: new Date(subscription.currentPeriodEnd || Date.now()).toISOString(),
          amount: subscription.amount || 299,
          billingInterval: subscription.billingInterval || 'month',
        } : null}
        isProcessing={isProcessingCancellation}
      />

      {/* Portal Return Handler */}
      {portalReturn.hasReturnData && (
        <PortalReturnHandler
          onReturnProcessed={(success, flow) => {
            console.log('Portal return processed:', success, flow);
            if (success) {
              // Refresh the page to show updated subscription data
              setTimeout(() => window.location.reload(), 1000);
            }
          }}
          autoRedirect={false} // We handle redirect manually
        />
      )}
    </PageLayout>
    </>
  );
};

export default SubscriptionManagementFixed;

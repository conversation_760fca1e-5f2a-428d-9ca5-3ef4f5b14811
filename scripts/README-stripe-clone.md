# Stripe Produktkloning: LIVE → TEST

Enkel løsning for å kopiere alle JobbLogg-produkter og priser fra live til test environment i Stripe.

## 🚀 Rask start

### 1. Installer krav
```bash
# Installer Stripe CLI
brew install stripe/stripe-cli/stripe

# Installer jq (JSON processor)
brew install jq
```

### 2. Sett miljøvariabler
```bash
# Sett dine Stripe API-nøkler
export STRIPE_LIVE_SECRET_KEY=sk_live_xxx
export STRIPE_TEST_SECRET_KEY=sk_test_xxx
```

### 3. <PERSON><PERSON><PERSON><PERSON> kloning
```bash
# Gå til scripts-mappen
cd scripts

# Gjør scriptet kjørbart
chmod +x stripe-clone-products.sh

# Kjør kloningen
./stripe-clone-products.sh
```

## 📋 Hva skjer

Scriptet gjør følgende automatisk:

1. **Eksporterer** alle produkter fra LIVE environment
2. **Kopierer** hvert produkt til TEST environment
3. **Henter alle priser** for hvert produkt fra LIVE
4. **Oppretter alle priser** i TEST environment
5. **Setter default_price** korrekt
6. **Bevarer metadata** og alle innstillinger
7. **Rydder opp** midlertidige filer

## 🎯 Resultat

Etter kjøring vil du ha:
- ✅ Alle produkter fra LIVE kopiert til TEST
- ✅ Alle priser (månedlig/årlig) kopiert
- ✅ Korrekte default_price innstillinger
- ✅ All metadata bevart
- ✅ Samme produktstruktur som LIVE

## 📊 Eksempel output

```
================================
JobbLogg Stripe Produktkloning
================================
ℹ️  Kopierer alle produkter og priser fra LIVE til TEST

================================
Sjekker krav
================================
✅ Stripe CLI funnet
✅ jq funnet
✅ Live Stripe key er satt
✅ Test Stripe key er satt
✅ Copy-script funnet

================================
Eksporterer produkter fra LIVE
================================
ℹ️  Henter alle produkter fra live environment...
✅ Eksporterte 3 produkter fra LIVE
ℹ️  Produkter som vil bli kopiert:
  - JobbLogg Basic (prod_xxx)
  - JobbLogg Professional (prod_yyy)
  - JobbLogg Enterprise (prod_zzz)

================================
Kopierer til TEST environment
================================
⚠️  Dette vil opprette produkter og priser i TEST environment
ℹ️  Eksisterende produkter med samme navn kan bli duplisert
Fortsett? (y/N): y

[2025-01-08 10:30:15] Starter kopi fra LIVE til TEST
[2025-01-08 10:30:15] Antall produkter i input-fil: 3
[2025-01-08 10:30:16] [1/3] Oppretter produkt i TEST: JobbLogg Basic
[2025-01-08 10:30:16]   Antall priser (LIVE): 2
[2025-01-08 10:30:17]   [1/2] Oppretter TEST-pris for price_xxx (29900 nok / month)
[2025-01-08 10:30:17]   [2/2] Oppretter TEST-pris for price_yyy (299900 nok / year)
[2025-01-08 10:30:18]   Default price satt til price_new_xxx
...

✅ Ferdig! Produkter og alle støttede priser kopiert til TEST.

================================
Sammendrag
================================
✅ Opprettet 3 produkter
✅ Opprettet 6 priser
ℹ️  Se stripe_copy_all_prices.log for detaljer
ℹ️  Produkter er nå tilgjengelige i TEST environment

✅ Ferdig! 🎉
```

## 🔧 Avanserte alternativer

### Kun eksport (uten kopiering)
```bash
# Eksporter bare til live_products.json
stripe products list --limit=100 --expand="data.default_price" --api-key="$STRIPE_LIVE_SECRET_KEY" > live_products.json
```

### Manuell kopiering
```bash
# Bruk det eksisterende scriptet direkte
export STRIPE_LIVE_KEY="$STRIPE_LIVE_SECRET_KEY"
export STRIPE_TEST_KEY="$STRIPE_TEST_SECRET_KEY"
export INPUT_FILE="live_products.json"
./copy_live_to_test_all_prices.sh
```

### Verifiser resultatet
```bash
# List produkter i TEST
stripe products list --api-key="$STRIPE_TEST_SECRET_KEY"

# List priser i TEST
stripe prices list --api-key="$STRIPE_TEST_SECRET_KEY"
```

## ⚠️ Viktige merknader

- **Duplikater**: Scriptet oppretter nye produkter, så kjør ikke flere ganger uten å rydde opp først
- **Metadata**: All metadata kopieres, inkludert JobbLogg-spesifikke innstillinger
- **Priser**: Alle pristyper støttes (månedlig, årlig, engangsbeløp)
- **Logging**: Detaljert logg lagres i `stripe_copy_all_prices.log`
- **Sikkerhet**: API-nøkler må være korrekt satt

## 🧹 Rydde opp i TEST

Hvis du trenger å fjerne produkter fra TEST:
```bash
# List alle produkter
stripe products list --api-key="$STRIPE_TEST_SECRET_KEY"

# Deaktiver et produkt (kan ikke slettes hvis det har priser)
stripe products update prod_xxx --api-key="$STRIPE_TEST_SECRET_KEY" -d active=false
```

## 🆘 Feilsøking

### "Stripe CLI er ikke installert"
```bash
brew install stripe/stripe-cli/stripe
```

### "jq er ikke installert"
```bash
brew install jq
```

### "STRIPE_LIVE_SECRET_KEY er ikke satt"
```bash
export STRIPE_LIVE_SECRET_KEY=sk_live_your_actual_key_here
```

### "copy_live_to_test_all_prices.sh ikke funnet"
Scriptet må være i samme mappe som `stripe-clone-products.sh`.

### Produkter blir duplisert
Dette er normalt - scriptet oppretter nye produkter hver gang. Deaktiver gamle produkter manuelt hvis nødvendig.

## 📞 Support

Se `stripe_copy_all_prices.log` for detaljerte feilmeldinger og kjøringslogg.

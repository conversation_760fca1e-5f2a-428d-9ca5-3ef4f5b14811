#!/usr/bin/env bash
set -Eeuo pipefail

# =========================
# Copy LIVE products → TEST (with all prices)
# =========================
# Usage:
#   export STRIPE_LIVE_KEY=sk_live_xxx
#   export STRIPE_TEST_KEY=sk_test_xxx
#   # Put your exported list in live_products.json (must contain .data[])
#   chmod +x copy_live_to_test_all_prices.sh
#   ./copy_live_to_test_all_prices.sh
#
# Optional:
#   INPUT_FILE=somefile.json ./copy_live_to_test_all_prices.sh
# =========================

INPUT_FILE="${INPUT_FILE:-live_products.json}"
LOG_FILE="stripe_copy_all_prices.log"
TMP_DIR=".stripe_copy_tmp"
mkdir -p "$TMP_DIR"

need_cmd() { command -v "$1" >/dev/null 2>&1 || { echo "❌ Mangler kommando: $1"; exit 1; }; }
need_cmd stripe
need_cmd jq

: "${STRIPE_LIVE_KEY:?Mangler STRIPE_LIVE_KEY sk_live_xxx}"
: "${STRIPE_TEST_KEY:?Mangler STRIPE_TEST_KEY sk_test_xxx}"

# Valider input JSON
if ! jq -e . "$INPUT_FILE" >/dev/null 2>&1; then
  echo "❌ $INPUT_FILE er ugyldig JSON (trunkert eller feil struktur)."
  read -r -p "Trykk Enter for å avslutte… " _
  exit 1
fi

timestamp() { date "+%Y-%m-%d %H:%M:%S"; }
log() { echo "[$(timestamp)] $*" | tee -a "$LOG_FILE"; }
err() { echo "[$(timestamp)] ❌ $*" | tee -a "$LOG_FILE" >&2; }

stripe_json() {
  local out
  if ! out=$(stripe "$@" 2>>"$LOG_FILE"); then
    err "Stripe feilet: stripe $*"
    return 1
  fi
  if ! echo "$out" | jq -e '.' >/dev/null 2>&1; then
    err "Ugyldig JSON fra stripe: stripe $*"
    {
      echo "----- RAW OUTPUT START -----"
      echo "$out"
      echo "----- RAW OUTPUT END -----"
    } >>"$LOG_FILE"
    return 1
  fi
  echo "$out"
}

log "Starter kopi fra LIVE til TEST"
total=$(jq '.data | length' "$INPUT_FILE")
log "Antall produkter i input-fil: $total"

i=0
jq -c '.data[]' "$INPUT_FILE" | while read -r product; do
  i=$((i+1))

  live_prod_id=$(echo "$product" | jq -r '.id')
  name=$(echo "$product" | jq -r '.name')
  description=$(echo "$product" | jq -r '.description // empty')
  active=$(echo "$product" | jq -r '.active // true')
  statement_descriptor=$(echo "$product" | jq -r '.statement_descriptor // empty')
  unit_label=$(echo "$product" | jq -r '.unit_label // empty')
  tax_code=$(echo "$product" | jq -r '.tax_code // empty')

  # Produkt-metadata (robust mot null)
  declare -a product_metadata_kv=()
  while IFS= read -r kv; do
    k=$(echo "$kv" | jq -r '.key')
    v=$(echo "$kv" | jq -r '.value')
    product_metadata_kv+=(-d "metadata[$k]=$v")
  done < <(echo "$product" | jq -c '(.metadata // {}) | to_entries[]?')

  log "[$i/$total] Oppretter produkt i TEST: $name  (LIVE $live_prod_id)"

  prod_args=(products create -d "name=$name")
  [[ -n "$description" ]] && prod_args+=(-d "description=$description")
  [[ "$active" == "false" ]] && prod_args+=(-d "active=false")
  [[ -n "$statement_descriptor" ]] && prod_args+=(-d "statement_descriptor=$statement_descriptor")
  [[ -n "$unit_label" ]] && prod_args+=(-d "unit_label=$unit_label")
  [[ -n "$tax_code" ]] && prod_args+=(-d "tax_code=$tax_code")
  # metadata
  if [[ ${#product_metadata_kv[@]} -gt 0 ]]; then
    prod_args+=("${product_metadata_kv[@]}")
  fi

  test_prod_json=$(stripe_json "${prod_args[@]}" --api-key "$STRIPE_TEST_KEY") || { err "Produktopprettelse feilet for $name"; continue; }
  test_prod_id=$(echo "$test_prod_json" | jq -r '.id')

  # --- Hent ALLE priser i LIVE (paginert) for dette produktet ---
  map_file="$TMP_DIR/price_map_${live_prod_id}.json"
  echo "{}" > "$map_file"

  starting_after=""
  all_prices_jsonl="$TMP_DIR/prices_${live_prod_id}.jsonl"
  : > "$all_prices_jsonl"

  while true; do
    if [[ -z "$starting_after" ]]; then
      resp=$(stripe_json prices list --limit=100 -d "product=$live_prod_id" --api-key "$STRIPE_LIVE_KEY") || { err "Klarte ikke hente priser for $live_prod_id"; break; }
    else
      resp=$(stripe_json prices list --limit=100 -d "product=$live_prod_id" --starting-after "$starting_after" --api-key "$STRIPE_LIVE_KEY") || { err "Klarte ikke hente flere priser for $live_prod_id"; break; }
    fi

    echo "$resp" | jq -c '.data[]?' >> "$all_prices_jsonl"
    has_more=$(echo "$resp" | jq -r '.has_more // false')
    if [[ "$has_more" != "true" ]]; then
      break
    fi
    starting_after=$(echo "$resp" | jq -r '.data[-1].id // empty')
    [[ -z "$starting_after" ]] && break
  done

  price_count=$(wc -l < "$all_prices_jsonl" | tr -d ' ')
  log "  Antall priser (LIVE): $price_count"

  # --- Opprett ALLE priser i TEST ---
  if [[ "$price_count" -gt 0 ]]; then
    line=0
    while IFS= read -r P; do
      line=$((line+1))
      live_price_id=$(echo "$P" | jq -r '.id')
      currency=$(echo "$P" | jq -r '.currency // empty')
      active_price=$(echo "$P" | jq -r '.active // true')
      nickname=$(echo "$P" | jq -r '.nickname // empty')
      tax_behavior=$(echo "$P" | jq -r '.tax_behavior // empty')
      unit_amount=$(echo "$P" | jq -r '.unit_amount // empty')
      unit_amount_decimal=$(echo "$P" | jq -r '.unit_amount_decimal // empty')
      billing_scheme=$(echo "$P" | jq -r '.billing_scheme // empty')
      tiers_mode=$(echo "$P" | jq -r '.tiers_mode // empty')
      transform_quantity=$(echo "$P" | jq -c '.transform_quantity // empty')
      price_type=$(echo "$P" | jq -r '.type // empty')

      recurring_interval=$(echo "$P" | jq -r '.recurring.interval // empty')
      interval_count=$(echo "$P" | jq -r '.recurring.interval_count // empty')
      usage_type=$(echo "$P" | jq -r '.recurring.usage_type // empty')
      aggregate_usage=$(echo "$P" | jq -r '.recurring.aggregate_usage // empty')
      trial_period_days=$(echo "$P" | jq -r '.recurring.trial_period_days // empty')

      # Pris-metadata (robust mot null)
      declare -a metadata_kv=()
      while IFS= read -r kv; do
        k=$(echo "$kv" | jq -r '.key')
        v=$(echo "$kv" | jq -r '.value')
        metadata_kv+=(-d "metadata[$k]=$v")
      done < <(echo "$P" | jq -c '(.metadata // {}) | to_entries[]?')

      # Ikke-støttede varianter i dette skriptet
      if [[ -n "$tiers_mode" && "$tiers_mode" != "null" ]]; then
        log "  [$line/$price_count] Hopper over LIVE price $live_price_id (tiers_mode=$tiers_mode)"
        continue
      fi
      if [[ "$transform_quantity" != "empty" && "$transform_quantity" != "null" ]]; then
        log "  [$line/$price_count] Hopper over LIVE price $live_price_id (transform_quantity)"
        continue
      fi

      if [[ -z "$currency" ]]; then
        log "  [$line/$price_count] Hopper over $live_price_id (mangler currency)"
        continue
      fi

      # Bygg opprettelses-args
      p_args=(prices create -d "currency=$currency" -d "product=$test_prod_id")
      [[ "$active_price" == "false" ]] && p_args+=(-d "active=false")
      [[ -n "$nickname" ]] && p_args+=(-d "nickname=$nickname")
      [[ -n "$tax_behavior" ]] && p_args+=(-d "tax_behavior=$tax_behavior")
      [[ -n "$billing_scheme" && "$billing_scheme" != "null" ]] && p_args+=(-d "billing_scheme=$billing_scheme")

      # Beløp (enten unit_amount eller unit_amount_decimal for faste priser)
      if [[ -n "$unit_amount" && "$unit_amount" != "null" ]]; then
        p_args+=(-d "unit_amount=$unit_amount")
      elif [[ -n "$unit_amount_decimal" && "$unit_amount_decimal" != "null" ]]; then
        p_args+=(-d "unit_amount_decimal=$unit_amount_decimal")
      else
        # tillat one_time uten beløp? Nei – da gir Stripe feil; hopp over
        if [[ "$price_type" != "one_time" ]]; then
          log "  [$line/$price_count] Hopper over $live_price_id (mangler unit_amount)"
          continue
        fi
      fi

      # Recurring
      if [[ -n "$recurring_interval" ]]; then
        p_args+=(-d "recurring[interval]=$recurring_interval")
        [[ -n "$interval_count" && "$interval_count" != "null" ]] && p_args+=(-d "recurring[interval_count]=$interval_count")
        [[ -n "$usage_type" && "$usage_type" != "null" ]] && p_args+=(-d "recurring[usage_type]=$usage_type")
        [[ -n "$aggregate_usage" && "$aggregate_usage" != "null" ]] && p_args+=(-d "recurring[aggregate_usage]=$aggregate_usage")
        [[ -n "$trial_period_days" && "$trial_period_days" != "null" ]] && p_args+=(-d "recurring[trial_period_days]=$trial_period_days")
      fi

      # metadata
      if [[ ${#metadata_kv[@]} -gt 0 ]]; then
        p_args+=("${metadata_kv[@]}")
      fi

      log "  [$line/$price_count] Oppretter TEST-pris for $live_price_id (${unit_amount:-$unit_amount_decimal} $currency ${recurring_interval:+/ $recurring_interval})"
      created_price_json=$(stripe_json "${p_args[@]}" --api-key "$STRIPE_TEST_KEY") || { err "Opprett pris feilet for $live_price_id"; continue; }
      test_price_id=$(echo "$created_price_json" | jq -r '.id')

      # Map LIVE → TEST for default_price senere
      jq --arg lp "$live_price_id" --arg tp "$test_price_id" '. + {($lp): $tp}' "$map_file" > "${map_file}.tmp" && mv "${map_file}.tmp" "$map_file"
    done < "$all_prices_jsonl"
  fi

  # Sett default_price i TEST tilsvarende LIVE, hvis mulig
  live_default_price=$(echo "$product" | jq -r '.default_price.id // .default_price // empty')
  if [[ -n "$live_default_price" && "$live_default_price" != "null" ]]; then
    test_default_price=$(jq -r --arg lp "$live_default_price" '.[$lp] // empty' "$map_file")
    if [[ -n "$test_default_price" ]]; then
      stripe_json products update "$test_prod_id" -d "default_price=$test_default_price" --api-key "$STRIPE_TEST_KEY" >/dev/null || log "  Kunne ikke sette default_price"
      log "  Default price satt til $test_default_price"
    else
      log "  Fant ikke test-ekvivalent for LIVE default_price $live_default_price"
    fi
  fi

done

log "✅ Ferdig! Produkter og alle støttede priser kopiert til TEST."
read -r -p "Trykk Enter for å avslutte… " _

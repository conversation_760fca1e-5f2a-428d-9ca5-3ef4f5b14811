#!/usr/bin/env bash
set -Eeuo pipefail

# =========================
# Enkel Stripe Produktkloning: LIVE → TEST
# =========================
# Dette scriptet:
# 1. Eksporterer alle produkter fra LIVE
# 2. Bruker det eksisterende copy_live_to_test_all_prices.sh scriptet
# 3. Kopierer alt til TEST med alle priser
# =========================

# Farger for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}$1${NC}"
    echo -e "${BLUE}================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# Sjekk at nødvendige kommandoer finnes
check_requirements() {
    print_header "Sjekker krav"
    
    if ! command -v stripe &> /dev/null; then
        print_error "Stripe CLI er ikke installert"
        print_info "Installer med: brew install stripe/stripe-cli/stripe"
        exit 1
    fi
    print_success "Stripe CLI funnet"
    
    if ! command -v jq &> /dev/null; then
        print_error "jq er ikke installert"
        print_info "Installer med: brew install jq"
        exit 1
    fi
    print_success "jq funnet"
    
    # Sjekk miljøvariabler
    if [[ -z "${STRIPE_LIVE_SECRET_KEY:-}" ]]; then
        print_error "STRIPE_LIVE_SECRET_KEY er ikke satt"
        print_info "Sett med: export STRIPE_LIVE_SECRET_KEY=sk_live_xxx"
        exit 1
    fi
    print_success "Live Stripe key er satt"
    
    if [[ -z "${STRIPE_TEST_SECRET_KEY:-}" ]]; then
        print_error "STRIPE_TEST_SECRET_KEY er ikke satt"
        print_info "Sett med: export STRIPE_TEST_SECRET_KEY=sk_test_xxx"
        exit 1
    fi
    print_success "Test Stripe key er satt"
    
    # Sjekk at copy-scriptet finnes
    if [[ ! -f "copy_live_to_test_all_prices.sh" ]]; then
        print_error "copy_live_to_test_all_prices.sh ikke funnet"
        print_info "Scriptet må være i samme mappe"
        exit 1
    fi
    print_success "Copy-script funnet"
}

# Eksporter produkter fra LIVE
export_products() {
    print_header "Eksporterer produkter fra LIVE"
    
    print_info "Henter alle produkter fra live environment..."
    
    # Eksporter alle produkter med expanded default_price
    if ! stripe products list \
        --limit=100 \
        --expand="data.default_price" \
        --api-key="$STRIPE_LIVE_SECRET_KEY" > live_products.json; then
        print_error "Kunne ikke eksportere produkter fra LIVE"
        exit 1
    fi
    
    # Sjekk at vi fikk data
    product_count=$(jq '.data | length' live_products.json)
    if [[ "$product_count" -eq 0 ]]; then
        print_warning "Ingen produkter funnet i LIVE environment"
        exit 1
    fi
    
    print_success "Eksporterte $product_count produkter fra LIVE"
    
    # Vis produktoversikt
    print_info "Produkter som vil bli kopiert:"
    jq -r '.data[] | "  - \(.name) (\(.id))"' live_products.json
}

# Kjør kopieringsprosessen
run_copy() {
    print_header "Kopierer til TEST environment"
    
    print_warning "Dette vil opprette produkter og priser i TEST environment"
    print_info "Eksisterende produkter med samme navn kan bli duplisert"
    
    read -p "Fortsett? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "Avbrutt av bruker"
        exit 0
    fi
    
    # Sett miljøvariabler for copy-scriptet
    export STRIPE_LIVE_KEY="$STRIPE_LIVE_SECRET_KEY"
    export STRIPE_TEST_KEY="$STRIPE_TEST_SECRET_KEY"
    export INPUT_FILE="live_products.json"
    
    # Kjør copy-scriptet
    print_info "Starter kopieringsprosess..."
    if ! bash copy_live_to_test_all_prices.sh; then
        print_error "Kopiering feilet"
        exit 1
    fi
    
    print_success "Kopiering fullført!"
}

# Rydd opp midlertidige filer
cleanup() {
    print_header "Rydder opp"
    
    if [[ -f "live_products.json" ]]; then
        rm live_products.json
        print_success "Fjernet live_products.json"
    fi
    
    if [[ -d ".stripe_copy_tmp" ]]; then
        rm -rf .stripe_copy_tmp
        print_success "Fjernet midlertidige filer"
    fi
    
    if [[ -f "stripe_copy_all_prices.log" ]]; then
        print_info "Loggfil beholdt: stripe_copy_all_prices.log"
    fi
}

# Vis sammendrag
show_summary() {
    print_header "Sammendrag"
    
    if [[ -f "stripe_copy_all_prices.log" ]]; then
        created_products=$(grep -c "Oppretter produkt i TEST" stripe_copy_all_prices.log || echo "0")
        created_prices=$(grep -c "Oppretter TEST-pris" stripe_copy_all_prices.log || echo "0")
        
        print_success "Opprettet $created_products produkter"
        print_success "Opprettet $created_prices priser"
        
        if [[ -s "stripe_copy_all_prices.log" ]]; then
            print_info "Se stripe_copy_all_prices.log for detaljer"
        fi
    fi
    
    print_info "Produkter er nå tilgjengelige i TEST environment"
    print_info "Du kan verifisere i Stripe Dashboard eller med: stripe products list --api-key=\$STRIPE_TEST_SECRET_KEY"
}

# Hovedfunksjon
main() {
    print_header "JobbLogg Stripe Produktkloning"
    print_info "Kopierer alle produkter og priser fra LIVE til TEST"
    echo
    
    # Sjekk krav
    check_requirements
    echo
    
    # Eksporter fra LIVE
    export_products
    echo
    
    # Kopier til TEST
    run_copy
    echo
    
    # Rydd opp
    cleanup
    echo
    
    # Vis sammendrag
    show_summary
    
    print_success "Ferdig! 🎉"
}

# Kjør hovedfunksjon
main "$@"

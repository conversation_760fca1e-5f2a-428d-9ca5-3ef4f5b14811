import { v } from "convex/values";
import Stripe from "stripe";
import { internal } from "./_generated/api";
import { action, internalAction, internalMutation, internalQuery, mutation, query } from "./_generated/server";

// Initialize Stripe function
function getStripe() {
  const secretKey = process.env.STRIPE_SECRET_KEY;
  if (!secretKey || secretKey === 'sk_test_your_secret_key_here') {
    throw new Error("Stripe secret key not configured");
  }
  return new Stripe(secretKey, {
    apiVersion: "2025-08-27.basil",
  });
}

// Check if we're in development mode without Stripe
function isStripeConfigured() {
  const secretKey = process.env.STRIPE_SECRET_KEY;
  return secretKey && secretKey !== 'sk_test_your_secret_key_here';
}

// Internal mutation to create trial subscription in database
export const createTrialSubscriptionInternal = internalMutation({
  args: {
    userId: v.string(),
    customerId: v.string(),
    selectedPlan: v.optional(v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise"))),
    selectedBilling: v.optional(v.union(v.literal("month"), v.literal("year"))),
    trialEnd: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    // Create subscription record with selected plan
    const subscriptionId = await ctx.db.insert("subscriptions", {
      userId: args.userId,
      stripeCustomerId: args.customerId,
      planLevel: args.selectedPlan || "basic",
      billingInterval: args.selectedBilling || "month",
      status: "trialing",
      trialStart: now,
      trialEnd: args.trialEnd,
      currentPeriodStart: now,
      currentPeriodEnd: args.trialEnd,
      createdAt: now,
      updatedAt: now,
      seats: 1,
    });

    // Update user record
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (existingUser) {
      await ctx.db.patch(existingUser._id, {
        trialEndsAt: args.trialEnd,
        trialUsedAt: now,
        hasCompletedTrial: false,
        updatedAt: now,
      });
    }

    return { subscriptionId, customerId: args.customerId };
  },
});

// Create trial subscription with Stripe customer
export const createTrialSubscription = action({
  args: {
    userId: v.string(),
    email: v.string(),
    name: v.string(),
    companyName: v.optional(v.string()),
    orgNumber: v.optional(v.string()),
    selectedPlan: v.optional(v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise"))),
    selectedBilling: v.optional(v.union(v.literal("month"), v.literal("year"))),
  },
  handler: async (ctx, args): Promise<{ subscriptionId: any; customerId: string; success: boolean }> => {
    // For now, skip validation checks to avoid type instantiation issues
    // TODO: Add back validation when Convex type system is more stable
    console.log('Creating trial subscription for user:', args.userId);

    const now = Date.now();
    const trialEnd = now + (7 * 24 * 60 * 60 * 1000); // 7 days from now

    let customerId = "dev_customer_" + args.userId; // Default for development

    // Only create Stripe customer if Stripe is configured
    if (isStripeConfigured()) {
      try {
        const stripe = getStripe();
        const customer = await stripe.customers.create({
          email: args.email,
          name: args.name,
          metadata: {
            userId: args.userId,
            companyName: args.companyName || "",
            orgNumber: args.orgNumber || ""
          },
          // tax: { validate_location: "immediately" } // Can be disabled for testing
        });
        customerId = customer.id;
      } catch (error) {
        console.error("Failed to create Stripe customer, using development mode:", error);
        // Continue with development customer ID
      }
    }

    // Create subscription record using internal mutation
    const result = await ctx.runMutation(internal.subscriptions.createTrialSubscriptionInternal, {
      userId: args.userId,
      customerId: customerId,
      selectedPlan: args.selectedPlan,
      selectedBilling: args.selectedBilling,
      trialEnd: trialEnd,
    });

    console.log('✅ Trial subscription created successfully:', result);
    return { ...result, success: true };
  },
});

// Get user's subscription (supports team members accessing company subscription)
export const getUserSubscription = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // First try to get user's own subscription
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // If no personal subscription, check if user is part of a team with a subscription
    if (!subscription) {
      // Get the user to check their company and role
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (user && user.contractorCompanyId) {
        // For non-administrators (utfoerende, prosjektleder), look up company administrator's subscription
        if (user.role === "utfoerende" || user.role === "prosjektleder") {
          const administrator = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .first();

          if (administrator) {
            subscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", administrator.clerkUserId))
              .first();
          }
        }
        // For newly promoted administrators, look for any existing subscription in the company
        else if (user.role === "administrator") {
          // First check if there's already an administrator with a subscription
          const existingAdminWithSubscription = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .collect();

          // Look for any administrator in the company who has a subscription
          for (const admin of existingAdminWithSubscription) {
            const adminSubscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", admin.clerkUserId))
              .first();

            if (adminSubscription) {
              subscription = adminSubscription;
              break;
            }
          }
        }
      }
    }

    return subscription;
  },
});

// Internal query to get user subscription (for use in actions)
export const getUserSubscriptionInternal = internalQuery({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // First try to get user's own subscription
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // If no personal subscription, check if user is part of a team with a subscription
    if (!subscription) {
      // Get the user to check their company and role
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (user && user.contractorCompanyId) {
        // For non-administrators (utfoerende, prosjektleder), look up company administrator's subscription
        if (user.role === "utfoerende" || user.role === "prosjektleder") {
          const administrator = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .first();

          if (administrator) {
            subscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", administrator.clerkUserId))
              .first();
          }
        }
        // For administrators, look for any existing subscription in the company
        else if (user.role === "administrator") {
          const existingAdminWithSubscription = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .collect();

          // Look for any administrator in the company who has a subscription
          for (const admin of existingAdminWithSubscription) {
            const adminSubscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", admin.clerkUserId))
              .first();

            if (adminSubscription) {
              subscription = adminSubscription;
              break;
            }
          }
        }
      }
    }

    return subscription;
  },
});

// Internal query to get user by Clerk ID
export const getUserByClerkId = internalQuery({
  args: { clerkUserId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();
  },
});

// Internal mutation to create subscription record
export const createSubscriptionRecord = internalMutation({
  args: {
    userId: v.string(),
    stripeCustomerId: v.string(),
    planLevel: v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise")),
    billingInterval: v.union(v.literal("month"), v.literal("year")),
    trialStart: v.number(),
    trialEnd: v.number(),
  },
  handler: async (ctx, args) => {
    const now = Date.now();

    return await ctx.db.insert("subscriptions", {
      userId: args.userId,
      stripeCustomerId: args.stripeCustomerId,
      status: "trialing",
      planLevel: args.planLevel,
      billingInterval: args.billingInterval,
      currentPeriodStart: now,
      currentPeriodEnd: args.trialEnd,
      trialStart: args.trialStart,
      trialEnd: args.trialEnd,
      seats: 1, // Start with 1 seat (the user themselves)
      createdAt: now,
      updatedAt: now,
    });
  },
});

// Internal mutation to update user trial status
export const updateUserTrialStatus = internalMutation({
  args: {
    userId: v.string(),
    trialEndsAt: v.number(),
    trialUsedAt: v.number(),
  },
  handler: async (ctx, args) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (user) {
      await ctx.db.patch(user._id, {
        subscriptionStatus: "trialing",
        trialEndsAt: args.trialEndsAt,
        trialUsedAt: args.trialUsedAt,
        updatedAt: Date.now(),
      });
    }
  },
});

// Internal mutation to update subscription portal URL
export const updateSubscriptionPortalUrl = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    portalUrl: v.string(),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.subscriptionId, {
      portalUrl: args.portalUrl,
      updatedAt: Date.now(),
    });
  },
});

// Test mutation
export const testMutation = mutation({
  args: {
    message: v.string(),
  },
  handler: async (_ctx, args) => {
    console.log('🔥 testMutation called with:', args);
    return { success: true, message: args.message };
  },
});

// Retrieve checkout session details for success page
export const getCheckoutSession = action({
  args: {
    sessionId: v.string(),
  },
  handler: async (ctx, args) => {
    // Get current user
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    const stripe = getStripe();

    try {
      console.log('🔄 Retrieving checkout session:', args.sessionId);

      // Retrieve session from Stripe
      const session = await stripe.checkout.sessions.retrieve(args.sessionId, {
        expand: ['subscription', 'customer', 'line_items']
      });

      // Verify this session belongs to the current user
      const subscription = await ctx.runQuery(internal.subscriptions.getUserSubscriptionInternal, {
        userId: identity.subject
      });

      if (!subscription) {
        throw new Error("No subscription found for user");
      }

      // Verify the session customer matches the user's subscription
      const sessionCustomerId = typeof session.customer === 'string'
        ? session.customer
        : session.customer?.id;

      if (sessionCustomerId !== subscription.stripeCustomerId) {
        throw new Error("Checkout session does not belong to current user");
      }

      console.log('✅ Checkout session retrieved successfully:', {
        sessionId: session.id,
        paymentStatus: session.payment_status,
        customerEmail: session.customer_details?.email,
        amountTotal: session.amount_total,
        currency: session.currency
      });

      // Return session details for the frontend
      return {
        sessionId: session.id,
        paymentStatus: session.payment_status,
        customerEmail: session.customer_details?.email || subscription.email,
        amountTotal: session.amount_total,
        currency: session.currency,
        subscriptionId: session.subscription,
        planLevel: subscription.planLevel,
        billingInterval: subscription.billingInterval,
        trialEnd: subscription.trialEnd,
        isTrialConversion: subscription.status === 'trialing' && session.payment_status === 'paid',
        createdAt: session.created * 1000, // Convert to milliseconds
      };

    } catch (error) {
      console.error('❌ Error retrieving checkout session:', error);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      throw new Error(`Failed to retrieve checkout session: ${errorMessage}`);
    }
  },
});

// Create Customer Portal session
export const createPortalSession = action({
  args: {
    userId: v.string(),
    returnUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log('🔥 createPortalSession called with:', args);
    console.log('createPortalSession called with args:', args);

    // Use internal query to find the subscription
    const subscription = await ctx.runQuery(internal.subscriptions.getUserSubscriptionInternal, {
      userId: args.userId
    });

    console.log('Final subscription found:', subscription ? 'Yes' : 'No');

    if (!subscription) {
      console.error('No subscription found for user:', args.userId);
      throw new Error("No subscription found for user. Please start a trial first.");
    }

    // In development mode without Stripe, return a mock URL
    if (!isStripeConfigured()) {
      console.log('Stripe not configured, using mock portal');
      const mockUrl = args.returnUrl || `${process.env.CONVEX_SITE_URL || 'http://localhost:5173'}/dashboard?mock_portal=true`;
      console.log('Mock URL:', mockUrl);

      // Update subscription with mock portal URL
      await ctx.runMutation(internal.subscriptions.updateSubscriptionPortalUrl, {
        subscriptionId: subscription._id,
        portalUrl: mockUrl,
      });

      return { url: mockUrl };
    }

    // Use real Stripe portal in production
    console.log('Stripe configured, creating real portal session');
    console.log('Customer ID:', subscription.stripeCustomerId);

    try {
      const stripe = getStripe();
      const session = await stripe.billingPortal.sessions.create({
        customer: subscription.stripeCustomerId,
        return_url: args.returnUrl || `${process.env.CONVEX_SITE_URL}/dashboard`,
      });

      console.log('Stripe portal session created:', session.url);

      // Update subscription with portal URL
      await ctx.runMutation(internal.subscriptions.updateSubscriptionPortalUrl, {
        subscriptionId: subscription._id,
        portalUrl: session.url,
      });

      return { url: session.url };
    } catch (stripeError) {
      console.error('Stripe portal session creation failed:', stripeError);
      const errorMessage = stripeError instanceof Error ? stripeError.message : 'Unknown error';
      throw new Error(`Failed to create Stripe portal session: ${errorMessage}`);
    }
  },
});

// Create Stripe Checkout session (legacy version - kept for compatibility)
export const createCheckoutSession = mutation({
  args: {
    userId: v.string(),
    priceId: v.string(),
    successUrl: v.optional(v.string()),
    cancelUrl: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const stripe = getStripe();

    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("No subscription found");
    }

    const session = await stripe.checkout.sessions.create({
      customer: subscription.stripeCustomerId,
      payment_method_types: ["card"],
      mode: "subscription",
      line_items: [
        {
          price: args.priceId,
          quantity: 1,
        },
      ],
      success_url: args.successUrl || `${process.env.CONVEX_SITE_URL}/dashboard?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: args.cancelUrl || `${process.env.CONVEX_SITE_URL}/dashboard`,
      billing_address_collection: "required",
      tax_id_collection: { enabled: true },
      allow_promotion_codes: true,
      customer_update: {
        address: "auto",
        name: "auto",
      },
      metadata: {
        userId: args.userId,
      },
    });

    return { url: session.url, sessionId: session.id };
  },
});

// Enhanced Stripe Checkout session creation with full feature support
export const createEnhancedCheckoutSession = action({
  args: {
    priceId: v.string(),
    planLevel: v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise")),
    billingInterval: v.union(v.literal("month"), v.literal("year")),
    quantity: v.optional(v.number()),
    successUrl: v.optional(v.string()),
    cancelUrl: v.optional(v.string()),
    trialDays: v.optional(v.number()),
    allowPromotionCodes: v.optional(v.boolean()),
    automaticTax: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Get current user
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Not authenticated");
    }

    // Check if Stripe is properly configured
    if (!isStripeConfigured()) {
      console.log('Stripe not configured - creating mock checkout session');

      // Return mock checkout session for development
      const mockSessionId = `cs_test_mock_${Date.now()}`;
      const mockUrl = `${process.env.CONVEX_SITE_URL || 'http://localhost:5173'}/checkout/success?session_id=${mockSessionId}&mock=true&plan=${args.planLevel}&billing=${args.billingInterval}`;

      console.log('✅ Mock checkout session created:', {
        sessionId: mockSessionId,
        url: mockUrl,
        planLevel: args.planLevel,
        billingInterval: args.billingInterval,
      });

      return {
        url: mockUrl,
        sessionId: mockSessionId,
        customerId: 'cus_mock_customer',
      };
    }

    const stripe = getStripe();

    // Find user's subscription using internal query
    const subscription = await ctx.runQuery(internal.subscriptions.getSubscriptionByUserInternal, {
      userId: identity.subject,
    });

    if (!subscription) {
      throw new Error("No subscription found for user");
    }

    // Prepare checkout session configuration
    const sessionConfig: Stripe.Checkout.SessionCreateParams = {
      customer: subscription.stripeCustomerId,
      payment_method_types: ["card"],
      mode: "subscription",
      line_items: [
        {
          price: args.priceId,
          quantity: args.quantity || 1,
        },
      ],
      success_url: args.successUrl || `${process.env.CONVEX_SITE_URL}/checkout/success?session_id={CHECKOUT_SESSION_ID}`,
      cancel_url: args.cancelUrl || `${process.env.CONVEX_SITE_URL}/checkout/cancel`,
      billing_address_collection: "required",
      tax_id_collection: { enabled: true },
      allow_promotion_codes: args.allowPromotionCodes ?? true,
      locale: "nb",
      customer_update: {
        address: "auto",
        name: "auto",
      },
      metadata: {
        userId: identity.subject,
        planLevel: args.planLevel,
        billingInterval: args.billingInterval,
      },
    };

    // Add automatic tax if enabled
    if (args.automaticTax ?? true) {
      sessionConfig.automatic_tax = { enabled: true };
    }

    // Add trial period if specified
    if (args.trialDays && args.trialDays > 0) {
      sessionConfig.subscription_data = {
        trial_period_days: args.trialDays,
      };
    }

    console.log('🔄 Creating enhanced checkout session:', {
      customerId: subscription.stripeCustomerId,
      priceId: args.priceId,
      planLevel: args.planLevel,
      billingInterval: args.billingInterval,
      quantity: args.quantity || 1,
      trialDays: args.trialDays,
    });

    try {
      const session = await stripe.checkout.sessions.create(sessionConfig);

      console.log('✅ Enhanced checkout session created:', {
        sessionId: session.id,
        url: session.url,
        customerId: session.customer,
      });

      return {
        url: session.url,
        sessionId: session.id,
        customerId: session.customer,
      };

    } catch (stripeError) {
      console.error('❌ Stripe checkout session creation failed:', stripeError);
      const errorMessage = stripeError instanceof Error ? stripeError.message : 'Unknown error';
      throw new Error(`Failed to create checkout session: ${errorMessage}`);
    }
  },
});

// Internal function to update subscription from webhook
export const updateSubscriptionFromWebhook = internalMutation({
  args: {
    stripeSubscriptionId: v.string(),
    status: v.string(),
    currentPeriodStart: v.number(),
    currentPeriodEnd: v.number(),
    cancelAt: v.optional(v.number()),
    cancelAtPeriodEnd: v.optional(v.boolean()),
    canceledAt: v.optional(v.number()),
    trialEnd: v.optional(v.number()),
    planLevel: v.optional(v.string()),
    billingInterval: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_subscription", (q) => q.eq("stripeSubscriptionId", args.stripeSubscriptionId))
      .first();

    if (!subscription) {
      console.error("Subscription not found for Stripe ID:", args.stripeSubscriptionId);
      return;
    }

    // Update subscription
    await ctx.db.patch(subscription._id, {
      status: args.status as any,
      currentPeriodStart: args.currentPeriodStart,
      currentPeriodEnd: args.currentPeriodEnd,
      cancelAt: args.cancelAt,
      cancelAtPeriodEnd: args.cancelAtPeriodEnd,
      canceledAt: args.canceledAt,
      trialEnd: args.trialEnd,
      planLevel: args.planLevel as any,
      billingInterval: args.billingInterval as any,
      updatedAt: Date.now(),
    });

    // Update user subscription status
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", subscription.userId))
      .first();

    if (user) {
      await ctx.db.patch(user._id, {
        subscriptionStatus: args.status as any,
        trialEndsAt: args.trialEnd,
        updatedAt: Date.now(),
      });
    }
  },
});

// Update plan during trial period
export const updateTrialPlan = mutation({
  args: {
    userId: v.string(),
    newPlanLevel: v.union(v.literal("basic"), v.literal("professional"), v.literal("enterprise")),
    newBillingInterval: v.union(v.literal("month"), v.literal("year")),
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    if (!subscription) {
      throw new Error("No subscription found");
    }

    // Only allow plan changes during trial period
    if (subscription.status !== "trialing") {
      throw new Error("Plan changes are only allowed during trial period");
    }

    const now = Date.now();

    // Update subscription with new plan details while preserving trial dates
    await ctx.db.patch(subscription._id, {
      planLevel: args.newPlanLevel,
      billingInterval: args.newBillingInterval,
      updatedAt: now,
      // Preserve trial dates - do NOT change trialStart or trialEnd
    });

    // Log the plan change
    await ctx.db.insert("subscriptionHistory", {
      subscriptionId: subscription._id,
      userId: args.userId,
      action: "plan_changed_during_trial",
      oldPlanLevel: subscription.planLevel,
      newPlanLevel: args.newPlanLevel,
      oldBillingInterval: subscription.billingInterval,
      newBillingInterval: args.newBillingInterval,
      timestamp: now,
    });

    return {
      success: true,
      newPlan: args.newPlanLevel,
      newBilling: args.newBillingInterval,
      trialEnd: subscription.trialEnd, // Return trial end to confirm it's preserved
    };
  },
});

// Get subscription status for access control (supports team members)
export const getSubscriptionStatus = query({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // First try to get user's own subscription
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // If no personal subscription, check if user is part of a team with a subscription
    if (!subscription) {
      // Get the user to check their company and role
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (user && user.contractorCompanyId) {
        // For non-administrators (utfoerende, prosjektleder), look up company administrator's subscription
        if (user.role === "utfoerende" || user.role === "prosjektleder") {
          const administrator = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .first();

          if (administrator) {
            subscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", administrator.clerkUserId))
              .first();
          }
        }
        // For newly promoted administrators, look for any existing subscription in the company
        else if (user.role === "administrator") {
          // First check if there's already an administrator with a subscription
          const existingAdminWithSubscription = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .collect();

          // Look for any administrator in the company who has a subscription
          for (const admin of existingAdminWithSubscription) {
            const adminSubscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", admin.clerkUserId))
              .first();

            if (adminSubscription) {
              subscription = adminSubscription;
              break;
            }
          }
        }
      }
    }

    if (!subscription) {
      return { hasSubscription: false };
    }

    const now = Date.now();
    const isTrialExpired = subscription.status === "trialing" && subscription.trialEnd && subscription.trialEnd < now;
    const hasActiveSubscription = subscription.status === "active";
    const isInTrial = subscription.status === "trialing" && !isTrialExpired;
    const isInGracePeriod = ["past_due", "incomplete", "unpaid"].includes(subscription.status);

    // Get downgrade status if subscription is past due
    let downgradeStatus = null;
    let downgradeRestrictions = null;

    if (subscription.status === "past_due" && subscription.downgradeStage !== undefined) {
      try {
        downgradeStatus = await ctx.runQuery(internal.subscriptionDowngrade.getSubscriptionDowngradeStatus, {
          subscriptionId: subscription._id,
        });
        downgradeRestrictions = downgradeStatus?.restrictions;
      } catch (error) {
        console.error('Error getting downgrade status:', error);
      }
    }

    // Apply downgrade restrictions if active
    const effectivePermissions = downgradeRestrictions ? {
      canCreateProjects: downgradeRestrictions.canCreateProjects,
      canAccessProjects: downgradeRestrictions.canAccessProjects,
      hasFullAccess: downgradeRestrictions.canCreateProjects &&
                     downgradeRestrictions.canUploadFiles &&
                     downgradeRestrictions.canInviteTeamMembers,
      isReadOnly: !downgradeRestrictions.canCreateProjects,
    } : {
      canCreateProjects: hasActiveSubscription || isInTrial,
      canAccessProjects: hasActiveSubscription || isInTrial || isInGracePeriod,
      hasFullAccess: hasActiveSubscription || isInTrial,
      isReadOnly: isInGracePeriod || isTrialExpired,
    };

    return {
      hasSubscription: true,
      subscription,
      hasActiveSubscription,
      isInTrial,
      isTrialExpired,
      isInGracePeriod,
      ...effectivePermissions,
      needsUpgrade: isTrialExpired || subscription.status === "past_due",
      downgradeStatus,
      isInDowngrade: downgradeStatus?.status === 'active',
      downgradeStage: downgradeStatus?.currentStage,
      downgradeRestrictions,
    };
  },
});

// Internal queries for actions (using existing getUserByClerkId)
export const getSubscriptionByUserInternal = internalQuery({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();
  },
});

// Internal query: get subscription by Stripe customer ID (for actions)
export const getByStripeCustomerIdInternal = internalQuery({
  args: { stripeCustomerId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_customer", (q) => q.eq("stripeCustomerId", args.stripeCustomerId))
      .first();
  },
});

// Internal query: get subscription by Stripe subscription ID (for actions)
export const getByStripeSubscriptionIdInternal = internalQuery({
  args: { stripeSubscriptionId: v.string() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptions")
      .withIndex("by_stripe_subscription", (q) => q.eq("stripeSubscriptionId", args.stripeSubscriptionId))
      .first();
  },
});


// Internal version of getSubscriptionStatus for debugging
export const getSubscriptionStatusInternal = internalQuery({
  args: { userId: v.string() },
  handler: async (ctx, args) => {
    // First try to get user's own subscription
    let subscription = await ctx.db
      .query("subscriptions")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .first();

    // If no personal subscription, check if user is part of a team with a subscription
    if (!subscription) {
      // Get the user to check their company and role
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
        .first();

      if (user && user.contractorCompanyId) {
        // For non-administrators (utfoerende, prosjektleder), look up company administrator's subscription
        if (user.role === "utfoerende" || user.role === "prosjektleder") {
          const administrator = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .first();

          if (administrator) {
            subscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", administrator.clerkUserId))
              .first();
          }
        }
        // For newly promoted administrators, look for any existing subscription in the company
        else if (user.role === "administrator") {
          // First check if there's already an administrator with a subscription
          const existingAdminWithSubscription = await ctx.db
            .query("users")
            .withIndex("by_contractor_company", (q) => q.eq("contractorCompanyId", user.contractorCompanyId))
            .filter((q) => q.eq(q.field("role"), "administrator"))
            .collect();

          // Look for any administrator in the company who has a subscription
          for (const admin of existingAdminWithSubscription) {
            const adminSubscription = await ctx.db
              .query("subscriptions")
              .withIndex("by_user", (q) => q.eq("userId", admin.clerkUserId))
              .first();

            if (adminSubscription) {
              subscription = adminSubscription;
              break;
            }
          }
        }
      }
    }

    if (!subscription) {
      return { hasSubscription: false };
    }

    const now = Date.now();
    const isTrialExpired = subscription.status === "trialing" && subscription.trialEnd && subscription.trialEnd < now;
    const hasActiveSubscription = subscription.status === "active";
    const isInTrial = subscription.status === "trialing" && !isTrialExpired;
    const isInGracePeriod = ["past_due", "incomplete", "unpaid"].includes(subscription.status);

    return {
      hasSubscription: true,
      subscription,
      hasActiveSubscription,
      isInTrial,
      isTrialExpired,
      isInGracePeriod,
      canCreateProjects: hasActiveSubscription || isInTrial,
      canAccessProjects: hasActiveSubscription || isInTrial || isInGracePeriod,
      hasFullAccess: hasActiveSubscription || isInTrial,
      isReadOnly: isInGracePeriod || isTrialExpired,
      needsUpgrade: isTrialExpired || subscription.status === "past_due",
    };
  },
});

// ===== WEBHOOK MONITORING AND ANALYTICS =====

/**
 * Get subscription events for analytics and debugging
 */
export const getSubscriptionEvents = query({
  args: {
    subscriptionId: v.optional(v.string()),
    eventType: v.optional(v.string()),
    limit: v.optional(v.number()),
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    let query = ctx.db.query("subscriptionEvents");

    // Apply filters
    if (args.subscriptionId) {
      query = query.withIndex("by_subscription", (q) => q.eq("subscriptionId", args.subscriptionId));
    } else if (args.eventType) {
      query = query.withIndex("by_event_type", (q) => q.eq("eventType", args.eventType));
    } else {
      query = query.withIndex("by_timestamp");
    }

    let events = await query.collect();

    // Apply time filters
    if (args.startTime || args.endTime) {
      events = events.filter(event => {
        if (args.startTime && event.timestamp < args.startTime) return false;
        if (args.endTime && event.timestamp > args.endTime) return false;
        return true;
      });
    }

    // Sort by timestamp (newest first)
    events.sort((a, b) => b.timestamp - a.timestamp);

    // Apply limit
    if (args.limit) {
      events = events.slice(0, args.limit);
    }

    return events;
  },
});

/**
 * Get subscription analytics summary
 */
export const getSubscriptionAnalytics = query({
  args: {
    days: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const days = args.days || 30;
    const startTime = Date.now() - (days * 24 * 60 * 60 * 1000);

    // Get all events in the time period
    const events = await ctx.db
      .query("subscriptionEvents")
      .withIndex("by_timestamp")
      .filter((q) => q.gte(q.field("timestamp"), startTime))
      .collect();

    // Calculate analytics
    const analytics = {
      totalEvents: events.length,
      eventsByType: {} as Record<string, number>,
      eventsByDay: {} as Record<string, number>,
      subscriptionMetrics: {
        created: 0,
        canceled: 0,
        reactivated: 0,
        trialConversions: 0,
        paymentSuccesses: 0,
        paymentFailures: 0,
      },
      recentEvents: events.slice(0, 10).map(event => ({
        eventType: event.eventType,
        timestamp: event.timestamp,
        subscriptionId: event.subscriptionId,
      })),
    };

    // Process events
    for (const event of events) {
      // Count by type
      analytics.eventsByType[event.eventType] = (analytics.eventsByType[event.eventType] || 0) + 1;

      // Count by day
      const dayKey = new Date(event.timestamp).toISOString().split('T')[0];
      analytics.eventsByDay[dayKey] = (analytics.eventsByDay[dayKey] || 0) + 1;

      // Update metrics
      switch (event.eventType) {
        case 'subscription_created':
          analytics.subscriptionMetrics.created++;
          break;
        case 'subscription_canceled':
          analytics.subscriptionMetrics.canceled++;
          break;
        case 'subscription_reactivated':
          analytics.subscriptionMetrics.reactivated++;
          break;
        case 'trial_converted':
          analytics.subscriptionMetrics.trialConversions++;
          break;
        case 'payment_succeeded':
          analytics.subscriptionMetrics.paymentSuccesses++;
          break;
        case 'payment_failed':
          analytics.subscriptionMetrics.paymentFailures++;
          break;
      }
    }

    return analytics;
  },
});

/**
 * Synchronize subscription status with Stripe
 * Ensures data consistency between Stripe and local database
 */
export const syncSubscriptionWithStripe = mutation({
  args: {
    subscriptionId: v.string(),
    forceSync: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    console.log(`🔄 Syncing subscription with Stripe: ${args.subscriptionId}`);

    if (!isStripeConfigured()) {
      console.log('Stripe not configured, skipping sync');
      return { success: false, error: 'Stripe not configured' };
    }

    try {
      const stripe = getStripe();

      // Find local subscription record
      const localSubscription = await ctx.db
        .query("subscriptions")
        .withIndex("by_stripe_subscription", (q) => q.eq("stripeSubscriptionId", args.subscriptionId))
        .first();

      if (!localSubscription) {
        return { success: false, error: 'Local subscription not found' };
      }

      // Fetch current data from Stripe
      const stripeSubscription = await stripe.subscriptions.retrieve(args.subscriptionId, {
        expand: ['customer', 'items.data.price.product'],
      });

      // Check if sync is needed
      const needsSync = args.forceSync ||
        localSubscription.status !== stripeSubscription.status ||
        localSubscription.currentPeriodEnd !== (stripeSubscription.current_period_end * 1000) ||
        localSubscription.trialEnd !== (stripeSubscription.trial_end ? stripeSubscription.trial_end * 1000 : null);

      if (!needsSync) {
        console.log('✅ Subscription already in sync');
        return { success: true, message: 'Already in sync' };
      }

      // Extract plan information from Stripe subscription
      const firstItem = stripeSubscription.items.data[0];
      const price = firstItem?.price;
      const product = price?.product;

      let planLevel: 'basic' | 'professional' | 'enterprise' = 'basic';
      let billingInterval: 'month' | 'year' = 'month';

      if (typeof product === 'object' && product.metadata) {
        planLevel = (product.metadata.planLevel as any) || 'basic';
      }

      if (price) {
        billingInterval = price.recurring?.interval === 'year' ? 'year' : 'month';
      }

      // Update local subscription with Stripe data
      const updateData = {
        status: stripeSubscription.status as any,
        planLevel,
        billingInterval,
        currentPeriodStart: stripeSubscription.current_period_start * 1000,
        currentPeriodEnd: stripeSubscription.current_period_end * 1000,
        trialEnd: stripeSubscription.trial_end ? stripeSubscription.trial_end * 1000 : null,
        cancelAt: stripeSubscription.cancel_at ? stripeSubscription.cancel_at * 1000 : null,
        cancelAtPeriodEnd: stripeSubscription.cancel_at_period_end || false,
        canceledAt: stripeSubscription.canceled_at ? stripeSubscription.canceled_at * 1000 : null,
        stripePriceId: price?.id,
        stripeProductId: typeof product === 'string' ? product : product?.id,
        updatedAt: Date.now(),
      };

      await ctx.db.patch(localSubscription._id, updateData);

      // Log sync event
      await ctx.db.insert("subscriptionEvents", {
        subscriptionId: args.subscriptionId,
        eventType: 'subscription_synced',
        eventData: {
          syncedFields: Object.keys(updateData),
          stripeStatus: stripeSubscription.status,
          localStatus: localSubscription.status,
          forceSync: args.forceSync,
        },
        timestamp: Date.now(),
        source: 'system_action',
        metadata: {
          syncReason: args.forceSync ? 'manual_force_sync' : 'automatic_sync',
        },
      });

      console.log(`✅ Subscription synced successfully: ${args.subscriptionId}`);
      return {
        success: true,
        message: 'Subscription synced successfully',
        updatedFields: Object.keys(updateData),
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.error(`❌ Error syncing subscription:`, errorMessage);

      return {
        success: false,
        error: errorMessage,
      };
    }
  },
});

/**
 * Validate webhook event processing integrity
 * Checks for missing or failed webhook events
 */
export const validateWebhookIntegrity = query({
  args: {
    hours: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const hours = args.hours || 24;
    const startTime = Date.now() - (hours * 60 * 60 * 1000);

    // Get all webhook events in the time period
    const webhookEvents = await ctx.db
      .query("webhookEvents")
      .filter((q) => q.gte(q.field("processedAt"), startTime))
      .collect();

    // Get all subscription events in the time period
    const subscriptionEvents = await ctx.db
      .query("subscriptionEvents")
      .withIndex("by_timestamp")
      .filter((q) => q.gte(q.field("timestamp"), startTime))
      .collect();

    // Analyze webhook processing
    const analysis = {
      timeRange: `${hours} hours`,
      webhookEvents: {
        total: webhookEvents.length,
        byType: {} as Record<string, number>,
      },
      subscriptionEvents: {
        total: subscriptionEvents.length,
        byType: {} as Record<string, number>,
        bySource: {} as Record<string, number>,
      },
      potentialIssues: [] as string[],
    };

    // Count webhook events by type
    for (const event of webhookEvents) {
      analysis.webhookEvents.byType[event.eventType] =
        (analysis.webhookEvents.byType[event.eventType] || 0) + 1;
    }

    // Count subscription events by type and source
    for (const event of subscriptionEvents) {
      analysis.subscriptionEvents.byType[event.eventType] =
        (analysis.subscriptionEvents.byType[event.eventType] || 0) + 1;

      const source = event.source || 'unknown';
      analysis.subscriptionEvents.bySource[source] =
        (analysis.subscriptionEvents.bySource[source] || 0) + 1;
    }

    // Check for potential issues
    const webhookEventCount = analysis.webhookEvents.total;
    const stripeWebhookEventCount = analysis.subscriptionEvents.bySource['stripe_webhook'] || 0;

    if (webhookEventCount === 0 && subscriptionEvents.length > 0) {
      analysis.potentialIssues.push('Subscription events exist but no webhook events recorded');
    }

    if (stripeWebhookEventCount < webhookEventCount * 0.8) {
      analysis.potentialIssues.push('Low ratio of Stripe webhook events to total webhook events');
    }

    return analysis;
  },
});

// ===== DUNNING MANAGEMENT SYSTEM =====

/**
 * Initialize default dunning configuration
 */
export const initializeDunningConfiguration = mutation({
  args: {},
  handler: async (ctx, args) => {
    console.log('🔄 Initializing default dunning configuration');

    // Check if default configuration already exists
    const existingConfig = await ctx.db
      .query("dunningConfigurations")
      .withIndex("by_plan_level", (q) => q.eq("planLevel", "default"))
      .first();

    if (existingConfig) {
      console.log('✅ Default dunning configuration already exists');
      return existingConfig;
    }

    // Create default dunning configuration
    const defaultConfig = {
      planLevel: "default" as const,
      maxAttempts: 4,
      retryIntervals: [24, 72, 168, 336], // 1 day, 3 days, 1 week, 2 weeks (in hours)
      escalationRules: [
        {
          attemptNumber: 1,
          escalationLevel: "initial" as const,
          communicationTemplates: ["payment_failed_initial"],
          gracePeriodHours: 24,
        },
        {
          attemptNumber: 2,
          escalationLevel: "reminder" as const,
          communicationTemplates: ["payment_failed_reminder"],
          gracePeriodHours: 72,
        },
        {
          attemptNumber: 3,
          escalationLevel: "urgent" as const,
          communicationTemplates: ["payment_failed_urgent"],
          gracePeriodHours: 168,
        },
        {
          attemptNumber: 4,
          escalationLevel: "final" as const,
          communicationTemplates: ["payment_failed_final"],
          gracePeriodHours: 336,
        },
      ],
      suspensionRules: {
        suspendAfterAttempts: 4,
        gracePeriodDays: 7,
        allowReactivation: true,
      },
      isActive: true,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const configId = await ctx.db.insert("dunningConfigurations", defaultConfig);
    console.log('✅ Default dunning configuration created:', configId);

    return { ...defaultConfig, _id: configId };
  },
});

/**
 * Start dunning process for a failed payment
 */
export const startDunningProcess = mutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    stripeInvoiceId: v.optional(v.string()),
    failureReason: v.optional(v.string()),
    metadata: v.optional(v.object({
      originalAmount: v.optional(v.number()),
      currency: v.optional(v.string()),
      paymentMethod: v.optional(v.string()),
      declineCode: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    console.log(`🔄 Starting dunning process for subscription: ${args.subscriptionId}`);

    // Get subscription details
    const subscription = await ctx.db.get(args.subscriptionId);
    if (!subscription) {
      throw new Error("Subscription not found");
    }

    // Get user details
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", subscription.userId))
      .first();

    if (!user) {
      throw new Error("User not found for subscription");
    }

    // Get dunning configuration (plan-specific or default)
    let dunningConfig = await ctx.db
      .query("dunningConfigurations")
      .withIndex("by_plan_level", (q) => q.eq("planLevel", subscription.planLevel))
      .first();

    if (!dunningConfig) {
      // Fall back to default configuration
      dunningConfig = await ctx.db
        .query("dunningConfigurations")
        .withIndex("by_plan_level", (q) => q.eq("planLevel", "default"))
        .first();
    }

    if (!dunningConfig) {
      throw new Error("No dunning configuration found");
    }

    // Check if there's already an active dunning process
    const existingDunning = await ctx.db
      .query("dunningAttempts")
      .withIndex("by_subscription_and_status", (q) =>
        q.eq("subscriptionId", args.subscriptionId).eq("status", "pending")
      )
      .first();

    if (existingDunning) {
      console.log(`⚠️ Dunning process already active for subscription: ${args.subscriptionId}`);
      return existingDunning;
    }

    // Calculate next retry time (first attempt)
    const nextRetryAt = Date.now() + (dunningConfig.retryIntervals[0] * 60 * 60 * 1000);

    // Create dunning attempt record
    const dunningAttempt = {
      subscriptionId: args.subscriptionId,
      userId: subscription.userId,
      stripeCustomerId: subscription.stripeCustomerId,
      stripeInvoiceId: args.stripeInvoiceId,
      attemptNumber: 1,
      maxAttempts: dunningConfig.maxAttempts,
      status: "pending" as const,
      failureReason: args.failureReason,
      nextRetryAt,
      escalationLevel: "initial" as const,
      communicationsSent: [],
      metadata: args.metadata,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const dunningId = await ctx.db.insert("dunningAttempts", dunningAttempt);

    // Send initial communication
    await sendDunningCommunication(ctx, dunningId, dunningConfig.escalationRules[0]);

    // Send payment failure notification
    try {
      await ctx.runAction(internal.paymentNotifications.sendPaymentFailureNotification, {
        userId: subscription.userId,
        subscriptionId: args.subscriptionId,
        failureType: 'unknown', // Will be determined by retry system
        failureReason: args.failureReason || 'Payment failed',
        amount: args.metadata?.originalAmount,
        currency: args.metadata?.currency,
      });
    } catch (notificationError) {
      console.error(`❌ Error sending payment failure notification:`, notificationError);
      // Don't throw - dunning process should continue
    }

    // Log dunning start event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId: args.subscriptionId,
      eventType: 'dunning_started',
      eventData: {
        dunningId,
        attemptNumber: 1,
        maxAttempts: dunningConfig.maxAttempts,
        nextRetryAt,
        failureReason: args.failureReason,
      },
      timestamp: Date.now(),
      source: 'system_action',
    });

    console.log(`✅ Dunning process started for subscription: ${args.subscriptionId}`);
    return { ...dunningAttempt, _id: dunningId };
  },
});

/**
 * Process dunning escalation for failed payment attempts
 */
export const processDunningEscalation = mutation({
  args: {
    dunningId: v.id("dunningAttempts"),
  },
  handler: async (ctx, args) => {
    console.log(`🔄 Processing dunning escalation: ${args.dunningId}`);

    const dunningAttempt = await ctx.db.get(args.dunningId);
    if (!dunningAttempt) {
      throw new Error("Dunning attempt not found");
    }

    if (dunningAttempt.status !== "pending") {
      console.log(`⚠️ Dunning attempt not in pending status: ${dunningAttempt.status}`);
      return dunningAttempt;
    }

    // Get dunning configuration
    const subscription = await ctx.db.get(dunningAttempt.subscriptionId);
    if (!subscription) {
      throw new Error("Subscription not found");
    }

    let dunningConfig = await ctx.db
      .query("dunningConfigurations")
      .withIndex("by_plan_level", (q) => q.eq("planLevel", subscription.planLevel))
      .first();

    if (!dunningConfig) {
      dunningConfig = await ctx.db
        .query("dunningConfigurations")
        .withIndex("by_plan_level", (q) => q.eq("planLevel", "default"))
        .first();
    }

    if (!dunningConfig) {
      throw new Error("No dunning configuration found");
    }

    // Check if we've reached maximum attempts
    if (dunningAttempt.attemptNumber >= dunningAttempt.maxAttempts) {
      console.log(`❌ Maximum dunning attempts reached for: ${args.dunningId}`);

      // Mark as failed and suspend if configured
      await ctx.db.patch(args.dunningId, {
        status: "failed",
        escalationLevel: "suspended",
        updatedAt: Date.now(),
      });

      // Suspend subscription access
      await suspendSubscriptionAccess(ctx, dunningAttempt.subscriptionId);

      // Initiate subscription downgrade process
      try {
        await ctx.runMutation(internal.subscriptionDowngrade.initiateSubscriptionDowngrade, {
          subscriptionId: dunningAttempt.subscriptionId,
          reason: 'Dunning process exhausted - payment failure beyond retry limits',
          triggeredBy: 'dunning_exhausted',
          metadata: {
            originalFailureReason: dunningAttempt.failureReason,
            dunningAttempts: dunningAttempt.attemptNumber,
          },
        });
      } catch (downgradeError) {
        console.error(`❌ Error initiating subscription downgrade:`, downgradeError);
        // Don't throw - dunning process should complete even if downgrade fails
      }

      return await ctx.db.get(args.dunningId);
    }

    // Escalate to next attempt
    const nextAttemptNumber = dunningAttempt.attemptNumber + 1;
    const nextRetryInterval = dunningConfig.retryIntervals[nextAttemptNumber - 1];
    const nextRetryAt = nextRetryInterval ? Date.now() + (nextRetryInterval * 60 * 60 * 1000) : null;

    // Find escalation rule for this attempt
    const escalationRule = dunningConfig.escalationRules.find(
      rule => rule.attemptNumber === nextAttemptNumber
    );

    if (!escalationRule) {
      console.error(`No escalation rule found for attempt ${nextAttemptNumber}`);
      return dunningAttempt;
    }

    // Update dunning attempt
    await ctx.db.patch(args.dunningId, {
      attemptNumber: nextAttemptNumber,
      nextRetryAt,
      escalationLevel: escalationRule.escalationLevel,
      lastAttemptAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Send escalation communication
    await sendDunningCommunication(ctx, args.dunningId, escalationRule);

    // Log escalation event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId: dunningAttempt.subscriptionId,
      eventType: 'dunning_escalated',
      eventData: {
        dunningId: args.dunningId,
        attemptNumber: nextAttemptNumber,
        escalationLevel: escalationRule.escalationLevel,
        nextRetryAt,
      },
      timestamp: Date.now(),
      source: 'system_action',
    });

    console.log(`✅ Dunning escalated to attempt ${nextAttemptNumber} for: ${args.dunningId}`);
    return await ctx.db.get(args.dunningId);
  },
});

/**
 * Resolve dunning process when payment succeeds
 */
export const resolveDunningProcess = mutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    resolvedBy: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log(`🔄 Resolving dunning process for subscription: ${args.subscriptionId}`);

    // Find active dunning attempts
    const activeDunning = await ctx.db
      .query("dunningAttempts")
      .withIndex("by_subscription_and_status", (q) =>
        q.eq("subscriptionId", args.subscriptionId).eq("status", "pending")
      )
      .collect();

    if (activeDunning.length === 0) {
      console.log(`ℹ️ No active dunning process found for subscription: ${args.subscriptionId}`);
      return null;
    }

    // Mark all active dunning attempts as completed
    for (const dunning of activeDunning) {
      await ctx.db.patch(dunning._id, {
        status: "completed",
        updatedAt: Date.now(),
      });

      // Log resolution event
      await ctx.db.insert("subscriptionEvents", {
        subscriptionId: args.subscriptionId,
        eventType: 'dunning_resolved',
        eventData: {
          dunningId: dunning._id,
          attemptNumber: dunning.attemptNumber,
          resolvedBy: args.resolvedBy || 'payment_success',
        },
        timestamp: Date.now(),
        source: 'system_action',
      });
    }

    // Reactivate subscription access if it was suspended
    await reactivateSubscriptionAccess(ctx, args.subscriptionId);

    console.log(`✅ Dunning process resolved for subscription: ${args.subscriptionId}`);
    return activeDunning.length;
  },
});

// ===== DUNNING HELPER FUNCTIONS =====

/**
 * Send dunning communication to customer
 */
async function sendDunningCommunication(
  ctx: any,
  dunningId: string,
  escalationRule: any
): Promise<void> {
  console.log(`📧 Sending dunning communication for: ${dunningId}`);

  try {
    const dunningAttempt = await ctx.db.get(dunningId);
    if (!dunningAttempt) {
      throw new Error("Dunning attempt not found");
    }

    // Get user details for communication
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", dunningAttempt.userId))
      .first();

    if (!user) {
      console.error(`User not found for dunning: ${dunningId}`);
      return;
    }

    // Get subscription details
    const subscription = await ctx.db.get(dunningAttempt.subscriptionId);
    if (!subscription) {
      console.error(`Subscription not found for dunning: ${dunningId}`);
      return;
    }

    // Prepare communication data
    const communicationData = {
      userEmail: user.email,
      userName: user.name || user.email,
      planLevel: subscription.planLevel,
      billingInterval: subscription.billingInterval,
      attemptNumber: dunningAttempt.attemptNumber,
      maxAttempts: dunningAttempt.maxAttempts,
      escalationLevel: escalationRule.escalationLevel,
      failureReason: dunningAttempt.failureReason,
      nextRetryAt: dunningAttempt.nextRetryAt,
      gracePeriodHours: escalationRule.gracePeriodHours,
    };

    // Send communications for each template
    const communicationsSent = [];

    for (const template of escalationRule.communicationTemplates) {
      try {
        // TODO: Integrate with email service (Resend)
        // For now, log the communication that would be sent
        console.log(`📧 Would send ${template} to ${user.email}:`, {
          template,
          escalationLevel: escalationRule.escalationLevel,
          attemptNumber: dunningAttempt.attemptNumber,
        });

        communicationsSent.push({
          type: "email" as const,
          template,
          sentAt: Date.now(),
          status: "sent" as const,
        });

      } catch (error) {
        console.error(`Failed to send ${template}:`, error);
        communicationsSent.push({
          type: "email" as const,
          template,
          sentAt: Date.now(),
          status: "failed" as const,
        });
      }
    }

    // Update dunning attempt with communication records
    const existingCommunications = dunningAttempt.communicationsSent || [];
    await ctx.db.patch(dunningId, {
      communicationsSent: [...existingCommunications, ...communicationsSent],
      updatedAt: Date.now(),
    });

    console.log(`✅ Dunning communications sent for: ${dunningId}`);

  } catch (error) {
    console.error(`❌ Error sending dunning communication:`, error);
    throw error;
  }
}

/**
 * Suspend subscription access due to payment failure
 */
async function suspendSubscriptionAccess(ctx: any, subscriptionId: string): Promise<void> {
  console.log(`🔒 Suspending subscription access: ${subscriptionId}`);

  try {
    const subscription = await ctx.db.get(subscriptionId);
    if (!subscription) {
      throw new Error("Subscription not found");
    }

    // Update subscription status
    await ctx.db.patch(subscriptionId, {
      status: "past_due",
      accessSuspendedAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Update user access flags
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", subscription.userId))
      .first();

    if (user) {
      await ctx.db.patch(user._id, {
        hasActiveSubscription: false,
        subscriptionStatus: "past_due",
        accessSuspendedAt: Date.now(),
        updatedAt: Date.now(),
      });
    }

    // Log suspension event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId,
      eventType: 'access_suspended',
      eventData: {
        reason: 'payment_failure',
        suspendedAt: Date.now(),
      },
      timestamp: Date.now(),
      source: 'system_action',
    });

    // Send subscription suspension notification
    try {
      await ctx.runAction(internal.paymentNotifications.sendSubscriptionSuspensionNotification, {
        userId: subscription.userId,
        subscriptionId,
        reason: 'payment_failure',
      });
    } catch (notificationError) {
      console.error(`❌ Error sending suspension notification:`, notificationError);
      // Don't throw - suspension should complete even if notification fails
    }

    console.log(`✅ Subscription access suspended: ${subscriptionId}`);

  } catch (error) {
    console.error(`❌ Error suspending subscription access:`, error);
    throw error;
  }
}

/**
 * Reactivate subscription access after payment resolution
 */
async function reactivateSubscriptionAccess(ctx: any, subscriptionId: string): Promise<void> {
  console.log(`🔓 Reactivating subscription access: ${subscriptionId}`);

  try {
    const subscription = await ctx.db.get(subscriptionId);
    if (!subscription) {
      throw new Error("Subscription not found");
    }

    // Only reactivate if it was suspended
    if (!subscription.accessSuspendedAt) {
      console.log(`ℹ️ Subscription was not suspended: ${subscriptionId}`);
      return;
    }

    // Update subscription status
    await ctx.db.patch(subscriptionId, {
      status: "active",
      accessSuspendedAt: undefined,
      reactivatedAt: Date.now(),
      updatedAt: Date.now(),
    });

    // Update user access flags
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", subscription.userId))
      .first();

    if (user) {
      await ctx.db.patch(user._id, {
        hasActiveSubscription: true,
        subscriptionStatus: "active",
        accessSuspendedAt: undefined,
        reactivatedAt: Date.now(),
        updatedAt: Date.now(),
      });
    }

    // Resolve any active downgrade
    try {
      await ctx.runMutation(internal.subscriptionDowngrade.resolveSubscriptionDowngrade, {
        subscriptionId,
        resolvedBy: 'payment_success',
        metadata: {
          reactivatedAt: Date.now(),
        },
      });
    } catch (downgradeError) {
      console.error(`❌ Error resolving subscription downgrade:`, downgradeError);
      // Don't throw - reactivation should complete even if downgrade resolution fails
    }

    // Log reactivation event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId,
      eventType: 'access_reactivated',
      eventData: {
        reason: 'payment_resolved',
        reactivatedAt: Date.now(),
      },
      timestamp: Date.now(),
      source: 'system_action',
    });

    console.log(`✅ Subscription access reactivated: ${subscriptionId}`);

  } catch (error) {
    console.error(`❌ Error reactivating subscription access:`, error);
    throw error;
  }
}

// ===== DUNNING QUERIES AND MONITORING =====

/**
 * Get active dunning attempts for a user
 */
export const getUserDunningAttempts = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("dunningAttempts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.neq(q.field("status"), "completed"))
      .collect();
  },
});

/**
 * Get dunning attempts that need processing
 */
export const getDunningAttemptsForProcessing = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const limit = args.limit || 50;

    return await ctx.db
      .query("dunningAttempts")
      .withIndex("by_next_retry")
      .filter((q) =>
        q.and(
          q.eq(q.field("status"), "pending"),
          q.lte(q.field("nextRetryAt"), now)
        )
      )
      .take(limit);
  },
});

/**
 * Get dunning statistics for monitoring
 */
export const getDunningStatistics = query({
  args: {
    days: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const days = args.days || 30;
    const startTime = Date.now() - (days * 24 * 60 * 60 * 1000);

    const allDunning = await ctx.db
      .query("dunningAttempts")
      .filter((q) => q.gte(q.field("createdAt"), startTime))
      .collect();

    const statistics = {
      totalAttempts: allDunning.length,
      byStatus: {} as Record<string, number>,
      byEscalationLevel: {} as Record<string, number>,
      averageAttempts: 0,
      resolutionRate: 0,
      suspensionRate: 0,
    };

    // Calculate statistics
    let totalAttemptNumbers = 0;
    let completedCount = 0;
    let suspendedCount = 0;

    for (const dunning of allDunning) {
      // Count by status
      statistics.byStatus[dunning.status] = (statistics.byStatus[dunning.status] || 0) + 1;

      // Count by escalation level
      statistics.byEscalationLevel[dunning.escalationLevel] =
        (statistics.byEscalationLevel[dunning.escalationLevel] || 0) + 1;

      // Track attempt numbers
      totalAttemptNumbers += dunning.attemptNumber;

      // Track outcomes
      if (dunning.status === "completed") completedCount++;
      if (dunning.escalationLevel === "suspended") suspendedCount++;
    }

    // Calculate rates
    if (allDunning.length > 0) {
      statistics.averageAttempts = totalAttemptNumbers / allDunning.length;
      statistics.resolutionRate = (completedCount / allDunning.length) * 100;
      statistics.suspensionRate = (suspendedCount / allDunning.length) * 100;
    }

    return statistics;
  },
});

/**
 * Cancel dunning process manually
 */
export const cancelDunningProcess = mutation({
  args: {
    dunningId: v.id("dunningAttempts"),
    reason: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    console.log(`🔄 Cancelling dunning process: ${args.dunningId}`);

    const dunningAttempt = await ctx.db.get(args.dunningId);
    if (!dunningAttempt) {
      throw new Error("Dunning attempt not found");
    }

    if (dunningAttempt.status !== "pending") {
      throw new Error(`Cannot cancel dunning in status: ${dunningAttempt.status}`);
    }

    // Update dunning attempt
    await ctx.db.patch(args.dunningId, {
      status: "cancelled",
      updatedAt: Date.now(),
    });

    // Log cancellation event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId: dunningAttempt.subscriptionId,
      eventType: 'dunning_cancelled',
      eventData: {
        dunningId: args.dunningId,
        reason: args.reason || 'manual_cancellation',
        cancelledAt: Date.now(),
      },
      timestamp: Date.now(),
      source: 'admin_action',
    });

    console.log(`✅ Dunning process cancelled: ${args.dunningId}`);
    return await ctx.db.get(args.dunningId);
  },
});

/**
 * Scheduled function to process dunning escalations
 * This should be called periodically (e.g., every hour) to handle dunning retries
 */
export const processDunningEscalations = mutation({
  args: {
    batchSize: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const batchSize = args.batchSize || 10;
    console.log(`🔄 Processing dunning escalations (batch size: ${batchSize})`);

    // Get dunning attempts that need processing
    const now = Date.now();
    const pendingDunning = await ctx.db
      .query("dunningAttempts")
      .withIndex("by_next_retry")
      .filter((q) =>
        q.and(
          q.eq(q.field("status"), "pending"),
          q.lte(q.field("nextRetryAt"), now)
        )
      )
      .take(batchSize);

    if (pendingDunning.length === 0) {
      console.log(`ℹ️ No dunning attempts ready for processing`);
      return { processed: 0, errors: 0 };
    }

    console.log(`📋 Found ${pendingDunning.length} dunning attempts to process`);

    let processed = 0;
    let errors = 0;

    // Process each dunning attempt
    for (const dunning of pendingDunning) {
      try {
        console.log(`🔄 Processing dunning escalation: ${dunning._id}`);

        // Mark as processing to prevent concurrent processing
        await ctx.db.patch(dunning._id, {
          status: "processing",
          updatedAt: Date.now(),
        });

        // Process the escalation
        await ctx.runMutation(internal.subscriptions.processDunningEscalation, {
          dunningId: dunning._id,
        });

        processed++;
        console.log(`✅ Processed dunning escalation: ${dunning._id}`);

      } catch (error) {
        errors++;
        console.error(`❌ Error processing dunning escalation ${dunning._id}:`, error);

        // Reset status back to pending for retry
        try {
          await ctx.db.patch(dunning._id, {
            status: "pending",
            updatedAt: Date.now(),
          });
        } catch (resetError) {
          console.error(`❌ Error resetting dunning status:`, resetError);
        }
      }
    }

    console.log(`✅ Dunning escalation processing complete:`, {
      processed,
      errors,
      total: pendingDunning.length,
    });

    return { processed, errors, total: pendingDunning.length };
  },
});

/**
 * Initialize dunning system with default configuration
 */
export const initializeDunningSystem = mutation({
  args: {},
  handler: async (ctx, args) => {
    console.log('🔄 Initializing dunning system');

    // Initialize default configuration
    const defaultConfig = await ctx.runMutation(internal.subscriptions.initializeDunningConfiguration, {});

    console.log('✅ Dunning system initialized with default configuration');
    return {
      success: true,
      defaultConfigId: defaultConfig._id,
      message: 'Dunning system initialized successfully',
    };
  },
});

// ===== PAYMENT RETRY SYSTEM =====

/**
 * Initialize default payment retry configurations
 */
export const initializePaymentRetryConfigurations = mutation({
  args: {},
  handler: async (ctx, args) => {
    console.log('🔄 Initializing payment retry configurations');

    // Check if configurations already exist
    const existingConfigs = await ctx.db
      .query("paymentRetryConfigurations")
      .withIndex("by_active", (q) => q.eq("isActive", true))
      .collect();

    if (existingConfigs.length > 0) {
      console.log('✅ Payment retry configurations already exist');
      return existingConfigs;
    }

    // Default retry configurations for different failure types
    const defaultConfigs = [
      {
        failureType: "card_declined" as const,
        maxRetryAttempts: 3,
        retryIntervals: [60, 240, 720], // 1 hour, 4 hours, 12 hours
        backoffStrategy: "exponential" as const,
        shouldRetry: true,
        escalateToDunning: true,
        notifyCustomer: false, // Don't notify for automatic retries
        priority: 10,
        isActive: true,
      },
      {
        failureType: "insufficient_funds" as const,
        maxRetryAttempts: 4,
        retryIntervals: [120, 480, 1440, 2880], // 2 hours, 8 hours, 24 hours, 48 hours
        backoffStrategy: "exponential" as const,
        shouldRetry: true,
        escalateToDunning: true,
        notifyCustomer: false,
        priority: 10,
        isActive: true,
      },
      {
        failureType: "expired_card" as const,
        maxRetryAttempts: 1,
        retryIntervals: [1440], // 24 hours (give customer time to update)
        backoffStrategy: "fixed" as const,
        shouldRetry: true,
        escalateToDunning: true,
        notifyCustomer: true, // Notify for card expiry
        priority: 10,
        isActive: true,
      },
      {
        failureType: "processing_error" as const,
        maxRetryAttempts: 5,
        retryIntervals: [30, 120, 360, 720, 1440], // 30min, 2h, 6h, 12h, 24h
        backoffStrategy: "exponential" as const,
        shouldRetry: true,
        escalateToDunning: true,
        notifyCustomer: false,
        priority: 10,
        isActive: true,
      },
      {
        failureType: "network_error" as const,
        maxRetryAttempts: 6,
        retryIntervals: [15, 60, 180, 360, 720, 1440], // 15min, 1h, 3h, 6h, 12h, 24h
        backoffStrategy: "exponential" as const,
        shouldRetry: true,
        escalateToDunning: false, // Network errors shouldn't escalate to dunning
        notifyCustomer: false,
        priority: 10,
        isActive: true,
      },
      {
        failureType: "authentication_required" as const,
        maxRetryAttempts: 2,
        retryIntervals: [720, 1440], // 12 hours, 24 hours
        backoffStrategy: "fixed" as const,
        shouldRetry: true,
        escalateToDunning: true,
        notifyCustomer: true, // Notify for 3D Secure requirement
        priority: 10,
        isActive: true,
      },
      {
        failureType: "default" as const,
        maxRetryAttempts: 3,
        retryIntervals: [60, 240, 720], // 1 hour, 4 hours, 12 hours
        backoffStrategy: "exponential" as const,
        shouldRetry: true,
        escalateToDunning: true,
        notifyCustomer: false,
        priority: 1, // Lowest priority (fallback)
        isActive: true,
      },
    ];

    const createdConfigs = [];
    for (const config of defaultConfigs) {
      const configId = await ctx.db.insert("paymentRetryConfigurations", {
        ...config,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      createdConfigs.push({ ...config, _id: configId });
    }

    console.log(`✅ Created ${createdConfigs.length} payment retry configurations`);
    return createdConfigs;
  },
});

/**
 * Start payment retry process for a failed payment
 */
export const startPaymentRetryProcess = mutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    stripeInvoiceId: v.string(),
    stripePaymentIntentId: v.optional(v.string()),
    originalFailureReason: v.string(),
    declineCode: v.optional(v.string()),
    metadata: v.optional(v.object({
      originalAmount: v.optional(v.number()),
      currency: v.optional(v.string()),
      paymentMethodType: v.optional(v.string()),
      customerEmail: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    console.log(`🔄 Starting payment retry process for subscription: ${args.subscriptionId}`);

    // Get subscription details
    const subscription = await ctx.db.get(args.subscriptionId);
    if (!subscription) {
      throw new Error("Subscription not found");
    }

    // Determine failure type from Stripe error
    const failureType = determineFailureType(args.originalFailureReason, args.declineCode);

    // Get retry configuration for this failure type and plan
    const retryConfig = await getRetryConfiguration(ctx, failureType, subscription.planLevel);

    if (!retryConfig || !retryConfig.shouldRetry) {
      console.log(`❌ No retry configuration or retries disabled for failure type: ${failureType}`);

      // Escalate directly to dunning if configured
      if (retryConfig?.escalateToDunning) {
        await escalateToPaymentDunning(ctx, args.subscriptionId, args.stripeInvoiceId, args.originalFailureReason);
      }

      return null;
    }

    // Check if there's already an active retry process for this invoice
    const existingRetry = await ctx.db
      .query("paymentRetryAttempts")
      .withIndex("by_stripe_invoice", (q) => q.eq("stripeInvoiceId", args.stripeInvoiceId))
      .filter((q) => q.neq(q.field("status"), "succeeded"))
      .filter((q) => q.neq(q.field("status"), "exhausted"))
      .filter((q) => q.neq(q.field("status"), "cancelled"))
      .first();

    if (existingRetry) {
      console.log(`⚠️ Payment retry already active for invoice: ${args.stripeInvoiceId}`);
      return existingRetry;
    }

    // Calculate first retry time
    const firstRetryInterval = retryConfig.retryIntervals[0];
    const nextRetryAt = Date.now() + (firstRetryInterval * 60 * 1000);

    // Create payment retry attempt record
    const retryAttempt = {
      subscriptionId: args.subscriptionId,
      userId: subscription.userId,
      stripeCustomerId: subscription.stripeCustomerId,
      stripeInvoiceId: args.stripeInvoiceId,
      stripePaymentIntentId: args.stripePaymentIntentId,
      attemptNumber: 1,
      maxRetryAttempts: retryConfig.maxRetryAttempts,
      status: "pending" as const,
      failureType,
      originalFailureReason: args.originalFailureReason,
      declineCode: args.declineCode,
      nextRetryAt,
      retryIntervals: retryConfig.retryIntervals,
      currentIntervalIndex: 0,
      retryResults: [],
      metadata: {
        ...args.metadata,
        planLevel: subscription.planLevel,
      },
      createdAt: Date.now(),
      updatedAt: Date.now(),
    };

    const retryId = await ctx.db.insert("paymentRetryAttempts", retryAttempt);

    // Log retry start event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId: args.subscriptionId,
      eventType: 'payment_retry_started',
      eventData: {
        retryId,
        failureType,
        maxRetryAttempts: retryConfig.maxRetryAttempts,
        nextRetryAt,
        originalFailureReason: args.originalFailureReason,
      },
      timestamp: Date.now(),
      source: 'system_action',
    });

    // Send payment failure notification with specific failure type
    try {
      await ctx.runAction(internal.paymentNotifications.sendPaymentFailureNotification, {
        userId: subscription.userId,
        subscriptionId: args.subscriptionId,
        failureType,
        failureReason: args.originalFailureReason,
        declineCode: args.declineCode,
        attemptNumber: 1,
        maxAttempts: retryConfig.maxRetryAttempts,
        nextRetryAt,
        amount: args.metadata?.originalAmount,
        currency: args.metadata?.currency,
        lastFourDigits: extractLastFourDigits(args.metadata?.paymentMethodType),
        invoiceUrl: args.metadata?.invoiceUrl,
      });
    } catch (notificationError) {
      console.error(`❌ Error sending payment failure notification:`, notificationError);
      // Don't throw - retry process should continue
    }

    console.log(`✅ Payment retry process started for subscription: ${args.subscriptionId}`);
    return { ...retryAttempt, _id: retryId };
  },
});

/**
 * Process payment retry attempt
 */
export const processPaymentRetry = action({
  args: {
    retryId: v.id("paymentRetryAttempts"),
  },
  handler: async (ctx, args) => {
    console.log(`🔄 Processing payment retry: ${args.retryId}`);

    const retryAttempt = await ctx.runQuery(internal.subscriptions.getPaymentRetryAttempt, {
      retryId: args.retryId,
    });

    if (!retryAttempt) {
      throw new Error("Payment retry attempt not found");
    }

    if (retryAttempt.status !== "pending") {
      console.log(`⚠️ Payment retry not in pending status: ${retryAttempt.status}`);
      return retryAttempt;
    }

    // Mark as processing
    await ctx.runMutation(internal.subscriptions.updatePaymentRetryStatus, {
      retryId: args.retryId,
      status: "processing",
    });

    const startTime = Date.now();
    let retryResult: any = {
      attemptNumber: retryAttempt.attemptNumber,
      attemptedAt: startTime,
      result: "failed" as const,
      failureReason: "Unknown error",
    };

    try {
      // Check if Stripe is configured for actual retry
      if (!isStripeConfigured()) {
        console.log('🧪 Stripe not configured - simulating retry success');
        retryResult = {
          ...retryResult,
          result: "succeeded" as const,
          processingTimeMs: Date.now() - startTime,
        };
      } else {
        // Attempt to retry the payment with Stripe
        const stripe = getStripe();

        try {
          // Retrieve the invoice to retry
          const invoice = await stripe.invoices.retrieve(retryAttempt.stripeInvoiceId);

          if (invoice.status === 'paid') {
            console.log(`✅ Invoice already paid: ${retryAttempt.stripeInvoiceId}`);
            retryResult = {
              ...retryResult,
              result: "succeeded" as const,
              processingTimeMs: Date.now() - startTime,
            };
          } else {
            // Attempt to pay the invoice
            const paidInvoice = await stripe.invoices.pay(retryAttempt.stripeInvoiceId);

            if (paidInvoice.status === 'paid') {
              console.log(`✅ Payment retry succeeded: ${retryAttempt.stripeInvoiceId}`);
              retryResult = {
                ...retryResult,
                result: "succeeded" as const,
                processingTimeMs: Date.now() - startTime,
              };
            } else {
              retryResult = {
                ...retryResult,
                result: "failed" as const,
                failureReason: `Invoice status: ${paidInvoice.status}`,
                processingTimeMs: Date.now() - startTime,
              };
            }
          }
        } catch (stripeError: any) {
          console.error(`❌ Stripe payment retry failed:`, stripeError);
          retryResult = {
            ...retryResult,
            result: "failed" as const,
            failureReason: stripeError.message || 'Stripe error',
            stripeErrorCode: stripeError.code,
            processingTimeMs: Date.now() - startTime,
          };
        }
      }

      // Update retry attempt with result
      await ctx.runMutation(internal.subscriptions.recordPaymentRetryResult, {
        retryId: args.retryId,
        retryResult,
      });

      if (retryResult.result === "succeeded") {
        // Payment succeeded - mark as succeeded and resolve
        await ctx.runMutation(internal.subscriptions.resolvePaymentRetry, {
          retryId: args.retryId,
        });

        console.log(`✅ Payment retry succeeded: ${args.retryId}`);
        return await ctx.runQuery(internal.subscriptions.getPaymentRetryAttempt, {
          retryId: args.retryId,
        });
      } else {
        // Payment failed - check if we should continue retrying
        const shouldContinue = await ctx.runMutation(internal.subscriptions.evaluateRetryEscalation, {
          retryId: args.retryId,
        });

        if (!shouldContinue) {
          console.log(`❌ Payment retry exhausted: ${args.retryId}`);
        }

        return await ctx.runQuery(internal.subscriptions.getPaymentRetryAttempt, {
          retryId: args.retryId,
        });
      }

    } catch (error) {
      console.error(`❌ Error processing payment retry:`, error);

      // Record the error
      retryResult = {
        ...retryResult,
        result: "error" as const,
        failureReason: error instanceof Error ? error.message : 'Unknown error',
        processingTimeMs: Date.now() - startTime,
      };

      await ctx.runMutation(internal.subscriptions.recordPaymentRetryResult, {
        retryId: args.retryId,
        retryResult,
      });

      // Reset status to pending for potential retry
      await ctx.runMutation(internal.subscriptions.updatePaymentRetryStatus, {
        retryId: args.retryId,
        status: "pending",
      });

      throw error;
    }
  },
});

// ===== PAYMENT RETRY HELPER FUNCTIONS =====

/**
 * Get payment retry attempt (internal query)
 */
export const getPaymentRetryAttempt = internalQuery({
  args: { retryId: v.id("paymentRetryAttempts") },
  handler: async (ctx, args) => {
    return await ctx.db.get(args.retryId);
  },
});

/**
 * Update payment retry status (internal mutation)
 */
export const updatePaymentRetryStatus = internalMutation({
  args: {
    retryId: v.id("paymentRetryAttempts"),
    status: v.union(
      v.literal("pending"),
      v.literal("processing"),
      v.literal("succeeded"),
      v.literal("failed"),
      v.literal("exhausted"),
      v.literal("cancelled")
    ),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.retryId, {
      status: args.status,
      updatedAt: Date.now(),
    });
  },
});

/**
 * Record payment retry result (internal mutation)
 */
export const recordPaymentRetryResult = internalMutation({
  args: {
    retryId: v.id("paymentRetryAttempts"),
    retryResult: v.object({
      attemptNumber: v.number(),
      attemptedAt: v.number(),
      result: v.union(
        v.literal("succeeded"),
        v.literal("failed"),
        v.literal("error")
      ),
      failureReason: v.optional(v.string()),
      stripeErrorCode: v.optional(v.string()),
      processingTimeMs: v.optional(v.number()),
    }),
  },
  handler: async (ctx, args) => {
    const retryAttempt = await ctx.db.get(args.retryId);
    if (!retryAttempt) {
      throw new Error("Payment retry attempt not found");
    }

    const updatedResults = [...retryAttempt.retryResults, args.retryResult];

    await ctx.db.patch(args.retryId, {
      retryResults: updatedResults,
      lastRetryAt: args.retryResult.attemptedAt,
      updatedAt: Date.now(),
    });
  },
});

/**
 * Resolve payment retry when successful (internal mutation)
 */
export const resolvePaymentRetry = internalMutation({
  args: { retryId: v.id("paymentRetryAttempts") },
  handler: async (ctx, args) => {
    const retryAttempt = await ctx.db.get(args.retryId);
    if (!retryAttempt) {
      throw new Error("Payment retry attempt not found");
    }

    // Mark as succeeded
    await ctx.db.patch(args.retryId, {
      status: "succeeded",
      updatedAt: Date.now(),
    });

    // Log success event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId: retryAttempt.subscriptionId,
      eventType: 'payment_retry_succeeded',
      eventData: {
        retryId: args.retryId,
        attemptNumber: retryAttempt.attemptNumber,
        totalAttempts: retryAttempt.retryResults.length,
        failureType: retryAttempt.failureType,
      },
      timestamp: Date.now(),
      source: 'system_action',
    });

    console.log(`✅ Payment retry resolved successfully: ${args.retryId}`);
  },
});

/**
 * Evaluate retry escalation (internal mutation)
 */
export const evaluateRetryEscalation = internalMutation({
  args: { retryId: v.id("paymentRetryAttempts") },
  handler: async (ctx, args) => {
    const retryAttempt = await ctx.db.get(args.retryId);
    if (!retryAttempt) {
      throw new Error("Payment retry attempt not found");
    }

    // Check if we've reached maximum attempts
    if (retryAttempt.attemptNumber >= retryAttempt.maxRetryAttempts) {
      console.log(`❌ Maximum retry attempts reached for: ${args.retryId}`);

      // Mark as exhausted
      await ctx.db.patch(args.retryId, {
        status: "exhausted",
        updatedAt: Date.now(),
      });

      // Escalate to dunning if configured
      const retryConfig = await getRetryConfigurationForAttempt(ctx, retryAttempt);
      if (retryConfig?.escalateToDunning) {
        await escalateToPaymentDunning(
          ctx,
          retryAttempt.subscriptionId,
          retryAttempt.stripeInvoiceId,
          retryAttempt.originalFailureReason
        );

        await ctx.db.patch(args.retryId, {
          escalatedToDunning: true,
          escalatedAt: Date.now(),
          updatedAt: Date.now(),
        });
      }

      // Log exhaustion event
      await ctx.db.insert("subscriptionEvents", {
        subscriptionId: retryAttempt.subscriptionId,
        eventType: 'payment_retry_exhausted',
        eventData: {
          retryId: args.retryId,
          totalAttempts: retryAttempt.attemptNumber,
          failureType: retryAttempt.failureType,
          escalatedToDunning: retryConfig?.escalateToDunning || false,
        },
        timestamp: Date.now(),
        source: 'system_action',
      });

      // Send retry exhausted notification
      try {
        await ctx.runAction(internal.paymentNotifications.sendPaymentRetryExhaustedNotification, {
          userId: retryAttempt.userId,
          subscriptionId: retryAttempt.subscriptionId,
          totalAttempts: retryAttempt.attemptNumber,
          lastFailureReason: retryAttempt.originalFailureReason,
          amount: retryAttempt.metadata?.originalAmount,
          currency: retryAttempt.metadata?.currency,
        });
      } catch (notificationError) {
        console.error(`❌ Error sending retry exhausted notification:`, notificationError);
        // Don't throw - retry process should continue
      }

      return false; // No more retries
    }

    // Schedule next retry
    const nextAttemptNumber = retryAttempt.attemptNumber + 1;
    const nextIntervalIndex = Math.min(
      retryAttempt.currentIntervalIndex + 1,
      retryAttempt.retryIntervals.length - 1
    );
    const nextInterval = retryAttempt.retryIntervals[nextIntervalIndex];
    const nextRetryAt = Date.now() + (nextInterval * 60 * 1000);

    await ctx.db.patch(args.retryId, {
      attemptNumber: nextAttemptNumber,
      currentIntervalIndex: nextIntervalIndex,
      nextRetryAt,
      status: "pending",
      updatedAt: Date.now(),
    });

    // Log escalation event
    await ctx.db.insert("subscriptionEvents", {
      subscriptionId: retryAttempt.subscriptionId,
      eventType: 'payment_retry_escalated',
      eventData: {
        retryId: args.retryId,
        attemptNumber: nextAttemptNumber,
        nextRetryAt,
        failureType: retryAttempt.failureType,
      },
      timestamp: Date.now(),
      source: 'system_action',
    });

    console.log(`🔄 Payment retry escalated to attempt ${nextAttemptNumber}: ${args.retryId}`);
    return true; // Continue retrying
  },
});

// ===== PAYMENT RETRY UTILITY FUNCTIONS =====

/**
 * Determine failure type from Stripe error message and decline code
 */
function determineFailureType(
  failureReason: string,
  declineCode?: string
): "card_declined" | "insufficient_funds" | "expired_card" | "incorrect_cvc" | "processing_error" | "authentication_required" | "generic_decline" | "network_error" | "unknown" {
  const reason = failureReason.toLowerCase();
  const code = declineCode?.toLowerCase();

  // Check decline code first (more specific)
  if (code) {
    switch (code) {
      case 'insufficient_funds':
        return 'insufficient_funds';
      case 'expired_card':
        return 'expired_card';
      case 'incorrect_cvc':
        return 'incorrect_cvc';
      case 'card_declined':
      case 'generic_decline':
        return 'card_declined';
      case 'authentication_required':
        return 'authentication_required';
      case 'processing_error':
        return 'processing_error';
    }
  }

  // Check failure reason text
  if (reason.includes('insufficient funds') || reason.includes('insufficient_funds')) {
    return 'insufficient_funds';
  }
  if (reason.includes('expired') || reason.includes('card expired')) {
    return 'expired_card';
  }
  if (reason.includes('cvc') || reason.includes('security code')) {
    return 'incorrect_cvc';
  }
  if (reason.includes('declined') || reason.includes('card was declined')) {
    return 'card_declined';
  }
  if (reason.includes('authentication') || reason.includes('3d secure')) {
    return 'authentication_required';
  }
  if (reason.includes('processing error') || reason.includes('processing_error')) {
    return 'processing_error';
  }
  if (reason.includes('network') || reason.includes('timeout') || reason.includes('connection')) {
    return 'network_error';
  }

  return 'unknown';
}

/**
 * Get retry configuration for failure type and plan level
 */
async function getRetryConfiguration(
  ctx: any,
  failureType: string,
  planLevel?: string
): Promise<any> {
  // First try to find plan-specific configuration
  if (planLevel) {
    const planSpecificConfig = await ctx.db
      .query("paymentRetryConfigurations")
      .withIndex("by_failure_type", (q) => q.eq("failureType", failureType))
      .filter((q) => q.eq(q.field("planLevel"), planLevel))
      .filter((q) => q.eq(q.field("isActive"), true))
      .first();

    if (planSpecificConfig) {
      return planSpecificConfig;
    }
  }

  // Try to find failure-type specific configuration
  const failureSpecificConfig = await ctx.db
    .query("paymentRetryConfigurations")
    .withIndex("by_failure_type", (q) => q.eq("failureType", failureType))
    .filter((q) => q.eq(q.field("isActive"), true))
    .first();

  if (failureSpecificConfig) {
    return failureSpecificConfig;
  }

  // Fall back to default configuration
  const defaultConfig = await ctx.db
    .query("paymentRetryConfigurations")
    .withIndex("by_failure_type", (q) => q.eq("failureType", "default"))
    .filter((q) => q.eq(q.field("isActive"), true))
    .first();

  return defaultConfig;
}

/**
 * Get retry configuration for an existing attempt
 */
async function getRetryConfigurationForAttempt(ctx: any, retryAttempt: any): Promise<any> {
  const subscription = await ctx.db.get(retryAttempt.subscriptionId);
  if (!subscription) {
    return null;
  }

  return await getRetryConfiguration(ctx, retryAttempt.failureType, subscription.planLevel);
}

/**
 * Escalate to payment dunning system
 */
async function escalateToPaymentDunning(
  ctx: any,
  subscriptionId: string,
  stripeInvoiceId: string,
  failureReason: string
): Promise<void> {
  console.log(`🔄 Escalating to dunning system: ${subscriptionId}`);

  try {
    await ctx.runMutation(internal.subscriptions.startDunningProcess, {
      subscriptionId,
      stripeInvoiceId,
      failureReason: `Payment retry exhausted: ${failureReason}`,
      metadata: {
        escalatedFromRetry: true,
        originalFailureReason: failureReason,
      },
    });

    console.log(`✅ Escalated to dunning system: ${subscriptionId}`);
  } catch (error) {
    console.error(`❌ Error escalating to dunning system:`, error);
    // Don't throw - this is a fallback mechanism
  }
}

/**
 * Extract last four digits from payment method information
 */
function extractLastFourDigits(paymentMethodType?: string): string | undefined {
  // This would typically come from Stripe payment method details
  // For now, return undefined as we don't have access to the full payment method
  return undefined;
}

// ===== PAYMENT RETRY QUERIES AND MONITORING =====

/**
 * Get payment retry attempts for processing
 */
export const getPaymentRetriesForProcessing = query({
  args: {
    limit: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const now = Date.now();
    const limit = args.limit || 50;

    return await ctx.db
      .query("paymentRetryAttempts")
      .withIndex("by_next_retry")
      .filter((q) =>
        q.and(
          q.eq(q.field("status"), "pending"),
          q.lte(q.field("nextRetryAt"), now)
        )
      )
      .take(limit);
  },
});

/**
 * Get user's payment retry attempts
 */
export const getUserPaymentRetries = query({
  args: {
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("paymentRetryAttempts")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => q.neq(q.field("status"), "succeeded"))
      .collect();
  },
});

/**
 * Get payment retry statistics
 */
export const getPaymentRetryStatistics = query({
  args: {
    days: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const days = args.days || 30;
    const startTime = Date.now() - (days * 24 * 60 * 60 * 1000);

    const allRetries = await ctx.db
      .query("paymentRetryAttempts")
      .filter((q) => q.gte(q.field("createdAt"), startTime))
      .collect();

    const statistics = {
      totalRetries: allRetries.length,
      byStatus: {} as Record<string, number>,
      byFailureType: {} as Record<string, number>,
      successRate: 0,
      averageAttempts: 0,
      escalationRate: 0,
    };

    let totalAttempts = 0;
    let successfulRetries = 0;
    let escalatedRetries = 0;

    for (const retry of allRetries) {
      // Count by status
      statistics.byStatus[retry.status] = (statistics.byStatus[retry.status] || 0) + 1;

      // Count by failure type
      statistics.byFailureType[retry.failureType] =
        (statistics.byFailureType[retry.failureType] || 0) + 1;

      // Track metrics
      totalAttempts += retry.attemptNumber;
      if (retry.status === "succeeded") successfulRetries++;
      if (retry.escalatedToDunning) escalatedRetries++;
    }

    // Calculate rates
    if (allRetries.length > 0) {
      statistics.successRate = (successfulRetries / allRetries.length) * 100;
      statistics.averageAttempts = totalAttempts / allRetries.length;
      statistics.escalationRate = (escalatedRetries / allRetries.length) * 100;
    }

    return statistics;
  },
});

/**
 * Process payment retries in batch
 */
export const processPaymentRetries = mutation({
  args: {
    batchSize: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const batchSize = args.batchSize || 10;
    console.log(`🔄 Processing payment retries (batch size: ${batchSize})`);

    // Get retries that need processing
    const now = Date.now();
    const pendingRetries = await ctx.db
      .query("paymentRetryAttempts")
      .withIndex("by_next_retry")
      .filter((q) =>
        q.and(
          q.eq(q.field("status"), "pending"),
          q.lte(q.field("nextRetryAt"), now)
        )
      )
      .take(batchSize);

    if (pendingRetries.length === 0) {
      console.log(`ℹ️ No payment retries ready for processing`);
      return { processed: 0, errors: 0 };
    }

    console.log(`📋 Found ${pendingRetries.length} payment retries to process`);

    let processed = 0;
    let errors = 0;

    // Process each retry attempt
    for (const retry of pendingRetries) {
      try {
        console.log(`🔄 Processing payment retry: ${retry._id}`);

        // Process the retry using the action
        await ctx.runAction(internal.subscriptions.processPaymentRetry, {
          retryId: retry._id,
        });

        processed++;
        console.log(`✅ Processed payment retry: ${retry._id}`);

      } catch (error) {
        errors++;
        console.error(`❌ Error processing payment retry ${retry._id}:`, error);
      }
    }

    console.log(`✅ Payment retry processing complete:`, {
      processed,
      errors,
      total: pendingRetries.length,
    });

    return { processed, errors, total: pendingRetries.length };
  },
});

/**
 * Initialize payment retry system
 */
export const initializePaymentRetrySystem = mutation({
  args: {},
  handler: async (ctx, args) => {
    console.log('🔄 Initializing payment retry system');

    // Initialize retry configurations
    const retryConfigs = await ctx.runMutation(internal.subscriptions.initializePaymentRetryConfigurations, {});

    console.log('✅ Payment retry system initialized');
    return {
      success: true,
      configurationsCreated: retryConfigs.length,
      message: 'Payment retry system initialized successfully',
    };
  },
});

// ===== SCHEDULED FUNCTIONS FOR CRON JOBS =====

/**
 * Process pending payment retries (scheduled function)
 */
export const processPendingPaymentRetries = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log('🔄 Processing pending payment retries...');

    try {
      const now = Date.now();

      // Get retry attempts that are due for processing
      const pendingRetries = await ctx.runQuery(internal.subscriptions.getPendingRetries, {
        currentTime: now
      });

      let processedCount = 0;

      for (const retry of pendingRetries) {
        try {
          await ctx.runAction(internal.subscriptions.processPaymentRetry, {
            retryAttemptId: retry._id
          });
          processedCount++;
        } catch (error) {
          console.error(`Failed to process retry ${retry._id}:`, error);
        }
      }

      console.log(`✅ Processed ${processedCount} payment retries`);
      return { processedCount };

    } catch (error) {
      console.error('❌ Error processing payment retries:', error);
      throw error;
    }
  },
});

/**
 * Process pending dunning escalations (scheduled function)
 */
export const processPendingDunningEscalations = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log('🔄 Processing pending dunning escalations...');

    try {
      const now = Date.now();

      // Get dunning attempts that are due for escalation
      const pendingEscalations = await ctx.runQuery(internal.subscriptions.getPendingDunningEscalations, {
        currentTime: now
      });

      let processedCount = 0;

      for (const dunning of pendingEscalations) {
        try {
          await ctx.runMutation(internal.subscriptions.escalateDunningAttempt, {
            dunningAttemptId: dunning._id
          });
          processedCount++;
        } catch (error) {
          console.error(`Failed to escalate dunning ${dunning._id}:`, error);
        }
      }

      console.log(`✅ Processed ${processedCount} dunning escalations`);
      return { processedCount };

    } catch (error) {
      console.error('❌ Error processing dunning escalations:', error);
      throw error;
    }
  },
});

/**
 * Send trial reminder emails (scheduled function)
 */
export const sendTrialReminders = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log('📧 Sending trial reminder emails...');

    try {
      const now = Date.now();
      const threeDaysFromNow = now + (3 * 24 * 60 * 60 * 1000);
      const oneDayFromNow = now + (24 * 60 * 60 * 1000);

      // Get trials expiring in 3 days
      const trialsExpiring3Days = await ctx.runQuery(internal.subscriptions.getTrialsExpiring, {
        startTime: now,
        endTime: threeDaysFromNow
      });

      // Get trials expiring in 1 day
      const trialsExpiring1Day = await ctx.runQuery(internal.subscriptions.getTrialsExpiring, {
        startTime: now,
        endTime: oneDayFromNow
      });

      let sentCount = 0;

      // Send 3-day reminders
      for (const subscription of trialsExpiring3Days) {
        try {
          // Send 3-day reminder email
          console.log(`Sending 3-day trial reminder for subscription ${subscription._id}`);
          sentCount++;
        } catch (error) {
          console.error(`Failed to send 3-day reminder for ${subscription._id}:`, error);
        }
      }

      // Send 1-day reminders
      for (const subscription of trialsExpiring1Day) {
        try {
          // Send 1-day reminder email
          console.log(`Sending 1-day trial reminder for subscription ${subscription._id}`);
          sentCount++;
        } catch (error) {
          console.error(`Failed to send 1-day reminder for ${subscription._id}:`, error);
        }
      }

      console.log(`✅ Sent ${sentCount} trial reminder emails`);
      return { sentCount };

    } catch (error) {
      console.error('❌ Error sending trial reminders:', error);
      throw error;
    }
  },
});

/**
 * Update subscription statuses (scheduled function)
 */
export const updateSubscriptionStatuses = internalAction({
  args: {},
  handler: async (ctx) => {
    console.log('🔄 Updating subscription statuses...');

    try {
      const now = Date.now();

      // Get subscriptions that need status updates
      const subscriptionsToUpdate = await ctx.runQuery(internal.subscriptions.getSubscriptionsNeedingStatusUpdate, {
        currentTime: now
      });

      let updatedCount = 0;

      for (const subscription of subscriptionsToUpdate) {
        try {
          await ctx.runMutation(internal.subscriptions.updateSubscriptionStatus, {
            subscriptionId: subscription._id,
            currentTime: now
          });
          updatedCount++;
        } catch (error) {
          console.error(`Failed to update subscription ${subscription._id}:`, error);
        }
      }

      console.log(`✅ Updated ${updatedCount} subscription statuses`);
      return { updatedCount };

    } catch (error) {
      console.error('❌ Error updating subscription statuses:', error);
      throw error;
    }
  },
});

// Helper queries for scheduled functions
export const getPendingRetries = internalQuery({
  args: { currentTime: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("paymentRetryAttempts")
      .withIndex("by_next_retry", (q) => q.lte("nextRetryAt", args.currentTime))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .collect();
  },
});

export const getPendingDunningEscalations = internalQuery({
  args: { currentTime: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("dunningAttempts")
      .withIndex("by_next_retry", (q) => q.lte("nextRetryAt", args.currentTime))
      .filter((q) => q.eq(q.field("status"), "pending"))
      .collect();
  },
});

export const getTrialsExpiring = internalQuery({
  args: { startTime: v.number(), endTime: v.number() },
  handler: async (ctx, args) => {
    return await ctx.db
      .query("subscriptions")
      .withIndex("by_trial_end", (q) =>
        q.gte("trialEnd", args.startTime).lte("trialEnd", args.endTime)
      )
      .filter((q) => q.eq(q.field("status"), "trialing"))
      .collect();
  },
});

export const getSubscriptionsNeedingStatusUpdate = internalQuery({
  args: { currentTime: v.number() },
  handler: async (ctx, args) => {
    // Get subscriptions with expired trials that are still marked as trialing
    const expiredTrials = await ctx.db
      .query("subscriptions")
      .withIndex("by_trial_end", (q) => q.lt("trialEnd", args.currentTime))
      .filter((q) => q.eq(q.field("status"), "trialing"))
      .collect();

    // Get subscriptions that should be canceled due to cancelAtPeriodEnd
    const subscriptionsToCancel = await ctx.db
      .query("subscriptions")
      .withIndex("by_current_period_end", (q) => q.lt("currentPeriodEnd", args.currentTime))
      .filter((q) =>
        q.and(
          q.eq(q.field("cancelAtPeriodEnd"), true),
          q.neq(q.field("status"), "canceled")
        )
      )
      .collect();

    console.log(`🔍 Found ${expiredTrials.length} expired trials and ${subscriptionsToCancel.length} subscriptions to cancel`);

    return [...expiredTrials, ...subscriptionsToCancel];
  },
});

export const updateSubscriptionStatus = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    currentTime: v.number()
  },
  handler: async (ctx, args) => {
    const subscription = await ctx.db.get(args.subscriptionId);
    if (!subscription) return;

    // Update expired trial to past_due status
    if (subscription.status === "trialing" && subscription.trialEnd && subscription.trialEnd < args.currentTime) {
      await ctx.db.patch(args.subscriptionId, {
        status: "past_due",
        updatedAt: args.currentTime,
      });

      // Log status change
      await ctx.db.insert("subscriptionEvents", {
        subscriptionId: args.subscriptionId,
        eventType: 'trial_expired',
        eventData: {
          previousStatus: 'trialing',
          newStatus: 'past_due',
          trialEnd: subscription.trialEnd,
        },
        timestamp: args.currentTime,
        source: 'system_action',
      });
    }

    // Handle subscriptions that should be canceled due to cancelAtPeriodEnd
    if (subscription.cancelAtPeriodEnd &&
        subscription.currentPeriodEnd &&
        subscription.currentPeriodEnd < args.currentTime &&
        subscription.status !== "canceled") {

      console.log(`🔄 Canceling subscription ${subscription._id} due to cancelAtPeriodEnd`);

      await ctx.db.patch(args.subscriptionId, {
        status: "canceled",
        canceledAt: args.currentTime,
        cancelAtPeriodEnd: false, // Reset since it's now actually canceled
        updatedAt: args.currentTime,
      });

      // Update user access flags
      const user = await ctx.db
        .query("users")
        .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", subscription.userId))
        .first();

      if (user) {
        await ctx.db.patch(user._id, {
          hasActiveSubscription: false,
          subscriptionStatus: "canceled",
          subscriptionCancelledAt: args.currentTime,
          updatedAt: args.currentTime,
        });
      }

      // Log status change
      await ctx.db.insert("subscriptionEvents", {
        subscriptionId: args.subscriptionId,
        eventType: 'canceled_at_period_end',
        eventData: {
          previousStatus: subscription.status,
          newStatus: 'canceled',
          currentPeriodEnd: subscription.currentPeriodEnd,
          cancelAtPeriodEnd: true,
        },
        timestamp: args.currentTime,
        source: 'system_action',
      });

      console.log(`✅ Successfully canceled subscription ${subscription._id} at period end`);
    }
  },
});

// Debug mutation to update subscription data
export const updateSubscription = internalMutation({
  args: {
    subscriptionId: v.id("subscriptions"),
    updateData: v.object({
      status: v.optional(v.string()),
      cancelAtPeriodEnd: v.optional(v.boolean()),
      cancelAt: v.optional(v.number()),
      canceledAt: v.optional(v.number()),
      currentPeriodStart: v.optional(v.number()),
      currentPeriodEnd: v.optional(v.number()),
      updatedAt: v.number(),
      manualSyncAt: v.optional(v.number()),
    }),
  },
  handler: async (ctx, args) => {
    await ctx.db.patch(args.subscriptionId, args.updateData);
  },
});

export const escalateDunningAttempt = internalMutation({
  args: { dunningAttemptId: v.id("dunningAttempts") },
  handler: async (ctx, args) => {
    const dunning = await ctx.db.get(args.dunningAttemptId);
    if (!dunning || dunning.status !== "pending") return;

    const nextAttempt = dunning.attemptNumber + 1;

    if (nextAttempt > dunning.maxAttempts) {
      // Mark as exhausted
      await ctx.db.patch(args.dunningAttemptId, {
        status: "exhausted",
        updatedAt: Date.now(),
      });
    } else {
      // Escalate to next attempt
      await ctx.db.patch(args.dunningAttemptId, {
        attemptNumber: nextAttempt,
        updatedAt: Date.now(),
      });
    }
  },
});
